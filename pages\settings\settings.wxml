<!--智能设置页面 - 严格按照原型图设计-->
<view class="page-container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-title">智能设置</view>
  </view>

  <!-- 用户信息卡片 -->
  <view class="user-card" bindtap="editUserInfo">
    <view class="user-info">
      <view class="user-avatar">
        <image
          src="{{userInfo.avatarUrl || userInfo.avatar}}"
          class="avatar-image"
          mode="aspectFill"
          lazy-load="true"
          binderror="onAvatarError"
          wx:if="{{userInfo.avatarUrl || userInfo.avatar}}"
        />
        <view wx:else class="avatar-placeholder">
          <text class="avatar-text">👤</text>
        </view>
      </view>
      <view class="user-details">
        <view class="user-name">{{userInfo.name || userInfo.nickName || '张老师'}}</view>
      </view>
    </view>
  </view>

  <!-- AI配置分组 -->
  <view class="settings-group">
    <view class="group-title">AI配置</view>
    
    <view class="setting-item" bindtap="goToAIConfig">
      <view class="setting-icon ai">
        <text>🤖</text>
      </view>
      <view class="setting-content">
        <view class="setting-title">评语风格偏好</view>
        <view class="setting-desc">设置默认的评语生成风格</view>
      </view>
      <view class="setting-action">
        <text>›</text>
      </view>
    </view>
    
  </view>

  <!-- 数据管理分组 -->
  <view class="settings-group">
    <view class="group-title">数据管理</view>



    <view class="setting-item" bindtap="goToReport">
      <view class="setting-icon data">
        <text>📈</text>
      </view>
      <view class="setting-content">
        <view class="setting-title">成长报告</view>
        <view class="setting-desc">查看使用统计和成长分析</view>
      </view>
      <view class="setting-action">
        <text>›</text>
      </view>
    </view>



    <view class="setting-item" bindtap="clearCache">
      <view class="setting-icon data">
        <text>🧹</text>
      </view>
      <view class="setting-content">
        <view class="setting-title">清理缓存</view>
        <view class="setting-desc">清理本地临时文件和缓存</view>
      </view>
      <view class="setting-action">
        <text>›</text>
      </view>
    </view>
  </view>

  <!-- 个性化设置分组 -->
  <view class="settings-group">
    <view class="group-title">个性化设置</view>

    <view class="setting-item" bindtap="toggleNotifications">
      <view class="setting-icon personalize">
        <text>🔔</text>
      </view>
      <view class="setting-content">
        <view class="setting-title">消息通知</view>
        <view class="setting-desc">接收重要提醒和更新通知</view>
      </view>
      <view class="toggle-switch {{systemSettings.notifications ? 'active' : ''}}" catchtap="toggleNotifications"></view>
    </view>

    <view class="setting-item" bindtap="toggleOfflineMode">
      <view class="setting-icon personalize">
        <text>📱</text>
      </view>
      <view class="setting-content">
        <view class="setting-title">离线模式</view>
        <view class="setting-desc">网络不佳时启用本地缓存</view>
      </view>
      <view class="toggle-switch {{systemSettings.offlineMode ? 'active' : ''}}" catchtap="toggleOfflineMode"></view>
    </view>
  </view>

  <!-- 隐私安全分组 -->
  <view class="settings-group">
    <view class="group-title">隐私安全</view>

    <view class="setting-item" bindtap="showPrivacyPolicy">
      <view class="setting-icon security">
        <text>🔒</text>
      </view>
      <view class="setting-content">
        <view class="setting-title">隐私政策</view>
        <view class="setting-desc">查看数据使用和隐私保护政策</view>
      </view>
      <view class="setting-action">
        <text>›</text>
      </view>
    </view>

    <view class="setting-item" bindtap="deleteAccount">
      <view class="setting-icon security danger">
        <text>⚠️</text>
      </view>
      <view class="setting-content">
        <view class="setting-title">注销账号</view>
        <view class="setting-desc">永久删除所有数据（不可恢复）</view>
      </view>
      <view class="setting-action">
        <text>›</text>
      </view>
    </view>
  </view>


  <!-- 帮助支持分组 -->
  <view class="settings-group">
    <view class="group-title">帮助支持</view>

    <view class="setting-item" bindtap="goToHelp">
      <view class="setting-icon help">
        <text>📖</text>
      </view>
      <view class="setting-content">
        <view class="setting-title">使用指南</view>
        <view class="setting-desc">查看详细使用教程和常见问题</view>
      </view>
      <view class="setting-action">
        <text>›</text>
      </view>
    </view>

    <view class="setting-item" bindtap="showFeedback">
      <view class="setting-icon help">
        <text>💬</text>
      </view>
      <view class="setting-content">
        <view class="setting-title">意见反馈</view>
        <view class="setting-desc">提交问题、建议或功能需求</view>
      </view>
      <view class="setting-action">
        <text>›</text>
      </view>
    </view>

    <view class="setting-item" bindtap="showAbout">
      <view class="setting-icon about">
        <text>ℹ️</text>
      </view>
      <view class="setting-content">
        <view class="setting-title">关于应用</view>
        <view class="setting-desc">版本信息、更新日志和开发团队</view>
      </view>
      <view class="setting-action">
        <text>›</text>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <view class="version-number">评语灵感君 v1.0.0</view>
    <view class="version-desc">AI智能评语助手</view>
    <view class="version-detail">让班主任更轻松</view>
  </view>
</view>