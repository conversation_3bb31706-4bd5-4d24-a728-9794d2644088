/**
 * 云开发服务层 - 增强版
 * 基于微信云开发的数据库操作，集成实时同步和连接管理
 */

// 确保模块正确加载
console.log('cloudService.js 模块开始加载');

// 导入实时同步管理器
const { realTimeSyncManager } = require('../utils/realTimeSyncManager');

// 全局云服务实例
let cloudServiceInstance = null;

/**
 * 云数据库服务
 */
class CloudService {
  constructor() {
    this.db = null;
    this.initialized = false;
    // 不在构造函数中立即初始化，等待云开发环境完全就绪
  }

  /**
   * 增强的云数据库初始化（支持重试和连接测试）
   */
  async init() {
    const MAX_RETRIES = 3;
    const RETRY_DELAYS = [500, 1000, 2000]; // 递增延迟
    
    for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {
      try {
        console.log(`[CloudService] 第${attempt + 1}次初始化尝试...`);
        
        // 检查微信云开发是否已初始化
        if (wx.cloud && typeof wx.cloud.database === 'function') {
          this.db = wx.cloud.database();
          
          // 执行连接测试
          await this.testConnection();
          
          this.initialized = true;
          this.lastConnectionTest = Date.now();
          console.log('[CloudService] 云数据库初始化成功');
          
          // 启动健康检查
          this.startHealthCheck();
          
          return this.db;
        } else {
          throw new Error('微信云开发尚未完全初始化');
        }
        
      } catch (error) {
        console.warn(`[CloudService] 第${attempt + 1}次初始化失败:`, error.message);
        
        if (attempt < MAX_RETRIES - 1) {
          const delay = RETRY_DELAYS[attempt];
          console.log(`[CloudService] ${delay}ms后重试...`);
          await this.delay(delay);
        } else {
          console.error('[CloudService] 所有初始化尝试均失败');
          throw new Error(`云服务初始化失败: ${error.message}`);
        }
      }
    }
  }

  /**
   * 连接测试
   */
  async testConnection() {
    try {
      // 执行简单查询测试连接
      const testResult = await this.db.collection('users').limit(1).get();
      console.log('[CloudService] 连接测试成功');
      return true;
    } catch (error) {
      console.error('[CloudService] 连接测试失败:', error);
      throw new Error('数据库连接测试失败');
    }
  }

  /**
   * 启动健康检查
   */
  startHealthCheck() {
    // 清除现有定时器
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
    
    // 每30秒检查一次连接健康状态
    this.healthCheckTimer = setInterval(async () => {
      try {
        await this.testConnection();
        this.lastConnectionTest = Date.now();
      } catch (error) {
        console.warn('[CloudService] 健康检查失败，尝试重新初始化:', error);
        this.initialized = false;
        try {
          await this.init();
        } catch (reinitError) {
          console.error('[CloudService] 重新初始化失败:', reinitError);
        }
      }
    }, 30000);
  }

  /**
   * 延迟工具函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 确保数据库已初始化
   */
  async ensureDbInitialized() {
    if (!this.initialized || !this.db) {
      await this.init();
    }
    return this.db;
  }

  /**
   * 用户登录
   */
  async login(code) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: { code }
      });

      if (result.result && result.result.success) {
        return result.result;
      } else {
        throw new Error(result.result?.error || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取当前用户ID
   */
  getCurrentUserId() {
    return new Promise((resolve, reject) => {
      // 调用云函数获取真实用户ID
      wx.cloud.callFunction({
        name: 'getUserId',
        success: (res) => {
          console.log('云函数调用成功，获取用户ID:', res.result.openid);
          resolve(res.result.openid);
        },
        fail: (error) => {
          console.error('云函数调用失败:', error);
          // 降级方案：使用本地缓存的用户ID或默认测试ID
          const cachedUserId = wx.getStorageSync('cached_user_id');
          if (cachedUserId) {
            console.log('使用缓存的用户ID:', cachedUserId);
            resolve(cachedUserId);
          } else {
            // 最后的降级方案：使用测试用户ID
            const testUserId = 'test_user_' + Date.now();
            wx.setStorageSync('cached_user_id', testUserId);
            console.log('使用测试用户ID:', testUserId);
            resolve(testUserId);
          }
        }
      });
    });
  }

  // ==================== 用户信息管理 ====================

  /**
   * 保存用户信息
   */
  async saveUserInfo(userInfo) {
    try {
      const userId = await this.getCurrentUserId();
      
      // 确保数据库已初始化
      const db = await this.ensureDbInitialized();
      
      const result = await db.collection('users').doc(userId).set({
        data: {
          ...userInfo,
          updateTime: new Date(),
          createTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('保存用户信息失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    try {
      const userId = await this.getCurrentUserId();

      // 尝试从云数据库获取用户信息
      try {
        // 确保数据库已初始化
        const db = await this.ensureDbInitialized();
        
        const result = await db.collection('users').doc(userId).get();

        if (result.data) {
          console.log('获取用户信息成功:', result.data);
          return { success: true, data: result.data };
        }
      } catch (dbError) {
        console.log('用户记录不存在，这是正常的首次使用情况');
      }

      // 用户不存在，自动创建默认用户
      console.log('用户信息不存在，自动创建默认用户');
      const defaultUserInfo = {
        name: '新用户',
        avatar: '',
        phone: '',
        email: '',
        school: '',
        subject: '',
        grade: ''
      };

      const createResult = await this.createUser(defaultUserInfo);
      if (createResult.success) {
        console.log('用户创建成功');
        return { success: true, data: { _id: userId, ...defaultUserInfo } };
      } else {
        console.error('创建用户失败:', createResult.error);
        return { success: false, message: '创建用户失败' };
      }

    } catch (error) {
      console.error('获取用户信息过程中发生错误:', error);
      return { success: false, error };
    }
  }

  /**
   * 创建用户
   */
  async createUser(userData) {
    try {
      const userId = await this.getCurrentUserId();

      const result = await this.db.collection('users').doc(userId).set({
        data: {
          ...userData,
          createTime: new Date(),
          updateTime: new Date()
        }
      });

      console.log('创建用户成功:', result);
      return { success: true, data: result };
    } catch (error) {
      console.error('创建用户失败:', error);
      return { success: false, error };
    }
  }

  // ==================== 班级管理 ====================

  /**
   * 创建班级
   */
  async createClass(classData) {
    try {
      const userId = await this.getCurrentUserId();
      
      const result = await this.db.collection('classes').add({
        data: {
          ...classData,
          teacherId: userId,
          createTime: new Date(),
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('创建班级失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 标准化数组数据处理
   */
  normalizeArrayData(result, dataType = 'data') {
    if (!result || !result.success || !result.data) {
      console.warn(`[数据标准化] ${dataType} 数据获取失败:`, result);
      return [];
    }
    
    const data = result.data;
    
    // 多种数据格式兼容
    if (Array.isArray(data)) return data;
    if (Array.isArray(data.data)) return data.data;
    if (Array.isArray(data.comments)) return data.comments;
    if (Array.isArray(data.students)) return data.students;
    if (Array.isArray(data.records)) return data.records;
    if (Array.isArray(data.classes)) return data.classes;
    
    console.warn(`[数据标准化] ${dataType} 数据格式不正确:`, typeof data);
    return [];
  }

  /**
   * 安全的数据获取方法
   */
  async safeGetData(method, options = {}) {
    try {
      const result = await this[method](options);
      return {
        success: true,
        data: this.normalizeArrayData(result, method)
      };
    } catch (error) {
      console.error(`[安全数据获取] ${method} 调用失败:`, error);
      return {
        success: false,
        data: [],
        error: error.message
      };
    }
  }

  /**
   * 获取班级列表
   */
  async getClassList() {
    try {
      const userId = await this.getCurrentUserId();
      
      const result = await this.db.collection('classes')
        .where({
          teacherId: userId
        })
        .orderBy('createTime', 'desc')
        .get();

      return { success: true, data: result.data };
    } catch (error) {
      console.error('获取班级列表失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 更新班级信息
   */
  async updateClass(classId, classData) {
    try {
      const result = await this.db.collection('classes').doc(classId).update({
        data: {
          ...classData,
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('更新班级失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 删除班级
   */
  async deleteClass(classId) {
    try {
      const result = await this.db.collection('classes').doc(classId).remove();
      return { success: true, data: result };
    } catch (error) {
      console.error('删除班级失败:', error);
      return { success: false, error };
    }
  }

  // ==================== 学生管理 ====================

  /**
   * 添加学生
   */
  async addStudent(studentData) {
    try {
      const userId = await this.getCurrentUserId();
      
      const result = await this.db.collection('students').add({
        data: {
          ...studentData,
          teacherId: userId,
          createTime: new Date(),
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('添加学生失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 批量添加学生
   */
  async batchAddStudents(studentsData) {
    try {
      const userId = await this.getCurrentUserId();
      const batch = [];

      studentsData.forEach(student => {
        batch.push({
          ...student,
          teacherId: userId,
          createTime: new Date(),
          updateTime: new Date()
        });
      });

      const result = await this.db.collection('students').add(batch);
      return { success: true, data: result };
    } catch (error) {
      console.error('批量添加学生失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 获取学生列表
   */
  async getStudentList(classId = null) {
    try {
      const userId = await this.getCurrentUserId();

      // 确保数据库已初始化
      const db = await this.ensureDbInitialized();

      // 从云数据库获取学生列表
      let query = db.collection('students').where({
        teacherId: userId
      });

      if (classId) {
        query = query.where({
          classId: classId
        });
      }

      const result = await query.orderBy('createTime', 'desc').get();
      // 生产环境：隐藏数据库查询详情
      return { success: true, data: result.data };
    } catch (error) {
      console.error('获取学生列表失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 获取学生详情
   */
  async getStudentDetail(studentId) {
    try {
      const userId = await this.getCurrentUserId();

      const result = await this.db.collection('students')
        .where({
          _id: studentId,
          teacherId: userId
        })
        .get();

      if (result.data.length === 0) {
        return { success: false, error: '学生不存在' };
      }

      return { success: true, data: result.data[0] };
    } catch (error) {
      console.error('获取学生详情失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 更新学生信息
   */
  async updateStudent(studentId, studentData) {
    try {
      const result = await this.db.collection('students').doc(studentId).update({
        data: {
          ...studentData,
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('更新学生失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 删除学生
   */
  async deleteStudent(studentId) {
    try {
      const result = await this.db.collection('students').doc(studentId).remove();
      return { success: true, data: result };
    } catch (error) {
      console.error('删除学生失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 清空所有学生数据（云端）
   */
  async clearAllStudents() {
    try {
      const userId = await this.getCurrentUserId();

      // 确保数据库已初始化
      const db = await this.ensureDbInitialized();

      // 获取当前用户的所有学生
      const studentsResult = await db.collection('students').where({
        teacherId: userId
      }).get();

      if (studentsResult.data.length === 0) {
        return { success: true, message: '没有学生数据需要清空' };
      }

      // 批量删除学生数据
      const deletePromises = studentsResult.data.map(student =>
        db.collection('students').doc(student._id).remove()
      );

      await Promise.all(deletePromises);

      // 同时清空相关的记录数据
      const recordsResult = await db.collection('records').where({
        teacherId: userId
      }).get();

      if (recordsResult.data.length > 0) {
        const deleteRecordPromises = recordsResult.data.map(record =>
          db.collection('records').doc(record._id).remove()
        );
        await Promise.all(deleteRecordPromises);
      }

      // 清空相关的评语数据
      const commentsResult = await db.collection('comments').where({
        teacherId: userId
      }).get();

      if (commentsResult.data.length > 0) {
        const deleteCommentPromises = commentsResult.data.map(comment =>
          db.collection('comments').doc(comment._id).remove()
        );
        await Promise.all(deleteCommentPromises);
      }

      console.log(`云端数据清空成功: 学生${studentsResult.data.length}个, 记录${recordsResult.data.length}条, 评语${commentsResult.data.length}条`);

      return {
        success: true,
        message: `清空成功: 学生${studentsResult.data.length}个, 记录${recordsResult.data.length}条, 评语${commentsResult.data.length}条`
      };
    } catch (error) {
      console.error('清空云端学生数据失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取用户统计数据
   */
  async getUserStats() {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      // 获取学生数量
      const studentsResult = await db.collection('students').where({
        teacherId: userId
      }).count();

      // 获取评语数量
      const commentsResult = await db.collection('comments').where({
        teacherId: userId
      }).count();

      // 获取记录数量
      const recordsResult = await db.collection('records').where({
        teacherId: userId
      }).count();

      // 获取班级数量
      const classesResult = await db.collection('classes').where({
        teacherId: userId
      }).count();

      const stats = {
        totalStudents: studentsResult.total || 0,
        totalComments: commentsResult.total || 0,
        totalRecords: recordsResult.total || 0,
        totalClasses: classesResult.total || 0,
        lastUpdated: new Date().toISOString()
      };

      return { success: true, data: stats };
    } catch (error) {
      console.error('获取用户统计数据失败:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== 记录管理 ====================

  /**
   * 添加记录
   */
  async addRecord(recordData) {
    try {
      const userId = await this.getCurrentUserId();

      const result = await this.db.collection('records').add({
        data: {
          ...recordData,
          teacherId: userId,
          createTime: new Date(),
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('添加记录失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 创建记录
   */
  async createRecord(recordData) {
    try {
      const userId = await this.getCurrentUserId();
      
      // 确保数据库已初始化
      const db = await this.ensureDbInitialized();

      const result = await db.collection('records').add({
        data: {
          ...recordData,
          teacherId: userId,
          createTime: new Date(),
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('创建记录失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 获取记录列表
   */
  async getRecordList(filters = {}) {
    try {
      const userId = await this.getCurrentUserId();
      
      // 确保数据库已初始化
      const db = await this.ensureDbInitialized();
      
      // 构建查询条件
      const whereConditions = {
        teacherId: userId
      };

      // 添加筛选条件
      if (filters.studentId) {
        whereConditions.studentId = filters.studentId;
      }
      if (filters.classId) {
        whereConditions.classId = filters.classId;
      }
      // behaviorType筛选条件暂时注释，因为存在字段名不一致问题
      // 将在查询后进行数据过滤
      let behaviorTypeFilter = filters.behaviorType;

      let query = db.collection('records').where(whereConditions);

      // 添加时间范围筛选
      if (filters.startDate && filters.endDate) {
        query = query.where({
          createTime: db.command.gte(filters.startDate).and(db.command.lte(filters.endDate))
        });
      } else if (filters.startDate) {
        query = query.where({
          createTime: db.command.gte(filters.startDate)
        });
      } else if (filters.endDate) {
        query = query.where({
          createTime: db.command.lte(filters.endDate)
        });
      }

      // 分页处理
      const pageSize = filters.pageSize || 20;
      const page = filters.page || 1;
      const skip = (page - 1) * pageSize;

      const result = await query
        .orderBy('createTime', 'desc')
        .skip(skip)
        .limit(pageSize)
        .get();

      // 处理字段名兼容和behaviorType过滤
      let processedData = result.data.map(record => {
        // 统一字段名：确保都有type和behaviorType字段
        const processedRecord = {
          ...record,
          type: record.type || record.behaviorType || 'general',
          behaviorType: record.behaviorType || record.type || 'general'
        };
        return processedRecord;
      });

      // 如果有behaviorType筛选条件，在这里进行过滤
      if (behaviorTypeFilter) {
        processedData = processedData.filter(record => 
          record.type === behaviorTypeFilter || record.behaviorType === behaviorTypeFilter
        );
      }

      console.log(`[记录查询] 原始数据数量: ${result.data.length}, 过滤后数量: ${processedData.length}`);
      return { success: true, data: processedData };
    } catch (error) {
      console.error('获取记录列表失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 更新记录
   */
  async updateRecord(recordId, recordData) {
    try {
      // 确保数据库已初始化
      const db = await this.ensureDbInitialized();
      
      const result = await db.collection('records').doc(recordId).update({
        data: {
          ...recordData,
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('更新记录失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 删除记录
   */
  async deleteRecord(recordId) {
    try {
      const result = await this.db.collection('records').doc(recordId).remove();
      return { success: true, data: result };
    } catch (error) {
      console.error('删除记录失败:', error);
      return { success: false, error };
    }
  }

  // ==================== AI评语生成 ====================

  /**
   * 生成AI评语
   */
  async generateAIComment(studentId, options = {}) {
    try {
      console.log('开始生成AI评语，学生ID:', studentId, '选项:', options);

      // 获取学生信息
      const studentResult = await this.getStudentDetail(studentId);
      if (!studentResult.success) {
        throw new Error('获取学生信息失败');
      }
      const student = studentResult.data;

      // 获取学生的行为记录
      const recordsResult = await this.getRecordList({
        studentId: studentId,
        startDate: options.startDate,
        endDate: options.endDate
      });

      if (!recordsResult.success) {
        throw new Error('获取学生记录失败');
      }

      const records = recordsResult.data || [];
      console.log('获取到学生记录数量:', records.length);

      // 构建提示词
      const prompt = this.buildCommentPrompt(student, records, options);
      console.log('构建的提示词:', prompt);

      // 调用豆包API云函数
      const result = await wx.cloud.callFunction({
        name: 'callDoubaoAPI',
        data: {
          prompt: prompt,
          style: options.style || 'warm',
          length: options.length || 'medium',
          temperature: 0.7,
          max_tokens: 300
        }
      });

      console.log('豆包API调用结果:', result);

      if (result.result && result.result.success) {
        return {
          success: true,
          data: {
            studentId: studentId,
            studentName: student.name,
            className: student.className,
            comment: result.result.data.content,
            style: options.style || 'warm',
            length: options.length || 'medium',
            generateTime: new Date().toLocaleString(),
            usage: result.result.data.usage
          }
        };
      } else {
        throw new Error(result.result?.error || 'AI评语生成失败');
      }

    } catch (error) {
      console.error('生成AI评语失败:', error);
      return {
        success: false,
        error: error.message || '生成AI评语失败'
      };
    }
  }

  /**
   * 构建评语生成提示词（优化版）
   */
  buildCommentPrompt(student, records, options) {
    const {
      style = 'warm',
      length = 'medium',
      focus = [],
      customRequirement = '',
      includeAdvice = true,
      includeEncouragement = true,
      startDate,
      endDate
    } = options;

    // 构建专业的教师身份设定
    let prompt = `你是一位有15年教学经验的资深教师，擅长用温暖而专业的语言为学生写评语。\n\n`;

    // 学生基本信息
    prompt += `学生信息：\n`;
    prompt += `- 姓名：${student.name}\n`;
    prompt += `- 班级：${student.className || '未指定班级'}\n`;

    // 时间范围
    if (startDate && endDate) {
      const start = new Date(startDate).toLocaleDateString();
      const end = new Date(endDate).toLocaleDateString();
      prompt += `- 评价时间段：${start} 至 ${end}\n`;
    }

    prompt += `\n`;

    // 学生表现分析（更详细的分类）
    if (records.length > 0) {
      prompt += `学生表现记录分析：\n`;

      // 按类型分类记录
      const recordsByType = {
        positive: records.filter(r => r.behaviorType === 'positive'),
        academic: records.filter(r => r.behaviorType === 'academic'),
        social: records.filter(r => r.behaviorType === 'social'),
        creative: records.filter(r => r.behaviorType === 'creative'),
        negative: records.filter(r => r.behaviorType === 'negative')
      };

      // 优势表现
      const strengths = [];
      if (recordsByType.positive.length > 0) {
        strengths.push(`品德表现：${recordsByType.positive.map(r => r.action).join('、')}`);
      }
      if (recordsByType.academic.length > 0) {
        strengths.push(`学习表现：${recordsByType.academic.map(r => r.action).join('、')}`);
      }
      if (recordsByType.social.length > 0) {
        strengths.push(`社交能力：${recordsByType.social.map(r => r.action).join('、')}`);
      }
      if (recordsByType.creative.length > 0) {
        strengths.push(`创新能力：${recordsByType.creative.map(r => r.action).join('、')}`);
      }

      if (strengths.length > 0) {
        prompt += `【优势表现】\n${strengths.join('\n')}\n\n`;
      }

      // 改进空间
      if (recordsByType.negative.length > 0) {
        prompt += `【改进空间】\n${recordsByType.negative.map(r => r.action).join('、')}\n\n`;
      }

      // 记录频次分析
      const recordCount = records.length;
      const positiveRatio = (recordsByType.positive.length + recordsByType.academic.length + recordsByType.social.length + recordsByType.creative.length) / recordCount;

      prompt += `【表现总结】\n`;
      prompt += `- 记录总数：${recordCount}条\n`;
      prompt += `- 积极表现占比：${Math.round(positiveRatio * 100)}%\n`;

    } else {
      prompt += `【表现记录】\n暂无具体行为记录，请基于学生的一般发展特点生成评语。\n\n`;
    }

    // 关注重点
    if (focus.length > 0) {
      prompt += `【重点关注领域】\n${focus.join('、')}\n\n`;
    }

    // 自定义要求
    if (customRequirement) {
      prompt += `【特殊要求】\n${customRequirement}\n\n`;
    }

    // 评语生成要求
    prompt += `【评语生成要求】\n`;
    prompt += `1. 语言风格：${this.getStyleDescription(style)}\n`;
    prompt += `2. 评语长度：${this.getLengthDescription(length)}\n`;
    prompt += `3. 结构要求：\n`;
    prompt += `   - 开头：总体评价（肯定学生的整体表现）\n`;
    prompt += `   - 中间：具体表现（基于记录的详细分析）\n`;

    if (includeAdvice) {
      prompt += `   - 建议：具体可行的改进建议\n`;
    }
    if (includeEncouragement) {
      prompt += `   - 结尾：温暖的鼓励和期望\n`;
    }

    prompt += `4. 内容要求：\n`;
    prompt += `   - 基于真实记录，不得编造事实\n`;
    prompt += `   - 语言温暖而专业，体现教师关爱\n`;
    prompt += `   - 避免空洞的套话，要有针对性\n`;
    prompt += `   - 字数控制在${this.getLengthRange(length)}字以内\n\n`;

    prompt += `请基于以上信息，为${student.name}同学生成一份专业、温暖、个性化的学生评语：`;

    return prompt;
  }

  /**
   * 获取风格描述（优化版）
   */
  getStyleDescription(style) {
    const styleMap = {
      'warm': '温暖亲切，充满关爱，用温和的语言表达对学生的关心和期望',
      'formal': '正式严谨，客观专业，用规范的教育语言进行客观评价',
      'encouraging': '积极向上，充满鼓励，重点突出学生的进步和潜力',
      'detailed': '详细具体，深入分析，全面细致地分析学生各方面表现'
    };
    return styleMap[style] || '温暖亲切，充满关爱';
  }

  /**
   * 获取长度描述
   */
  getLengthDescription(length) {
    const lengthMap = {
      'short': '简洁明了，重点突出',
      'medium': '适中详细，全面均衡',
      'long': '详细全面，深入分析'
    };
    return lengthMap[length] || '适中详细，全面均衡';
  }

  /**
   * 获取长度范围
   */
  getLengthRange(length) {
    const rangeMap = {
      'short': '50-80',
      'medium': '100-150',
      'long': '200-300'
    };
    return rangeMap[length] || '100-150';
  }

  /**
   * 获取评语统计信息
   */
  async getCommentStatistics() {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      // 获取总评语数
      const totalResult = await db.collection('comments')
        .where({ teacherId: userId })
        .count();

      // 获取本月评语数
      const now = new Date();
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const monthResult = await db.collection('comments')
        .where({
          teacherId: userId,
          createTime: db.command.gte(monthStart)
        })
        .count();

      // 获取本周评语数
      const weekStart = new Date();
      weekStart.setDate(weekStart.getDate() - weekStart.getDay());
      weekStart.setHours(0, 0, 0, 0);
      const weekResult = await db.collection('comments')
        .where({
          teacherId: userId,
          createTime: db.command.gte(weekStart)
        })
        .count();

      // 获取今日评语数
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);
      const todayResult = await db.collection('comments')
        .where({
          teacherId: userId,
          createTime: db.command.gte(todayStart)
        })
        .count();

      // 获取风格分布
      const styleStats = await this.getCommentStyleStats(userId, db);

      return {
        success: true,
        data: {
          total: totalResult.total || 0,
          thisMonth: monthResult.total || 0,
          thisWeek: weekResult.total || 0,
          today: todayResult.total || 0,
          styleDistribution: styleStats
        }
      };
    } catch (error) {
      console.error('获取评语统计失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取评语风格统计
   */
  async getCommentStyleStats(userId, db) {
    try {
      const styles = ['warm', 'formal', 'encouraging', 'detailed'];
      const styleStats = {};

      for (const style of styles) {
        const result = await db.collection('comments')
          .where({
            teacherId: userId,
            style: style
          })
          .count();
        styleStats[style] = result.total || 0;
      }

      return styleStats;
    } catch (error) {
      console.error('获取风格统计失败:', error);
      return {};
    }
  }

  /**
   * 获取学生行为记录统计
   */
  async getRecordStatistics() {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      // 获取总记录数
      const totalResult = await db.collection('records')
        .where({ teacherId: userId })
        .count();

      // 获取本月记录数
      const now = new Date();
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const monthResult = await db.collection('records')
        .where({
          teacherId: userId,
          createTime: db.command.gte(monthStart)
        })
        .count();

      // 获取记录类型分布
      const typeStats = await this.getRecordTypeStats(userId, db);

      return {
        success: true,
        data: {
          total: totalResult.total || 0,
          thisMonth: monthResult.total || 0,
          typeDistribution: typeStats
        }
      };
    } catch (error) {
      console.error('获取记录统计失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取记录类型统计
   */
  async getRecordTypeStats(userId, db) {
    try {
      const types = ['positive', 'negative', 'neutral'];
      const typeStats = {};

      for (const type of types) {
        const result = await db.collection('records')
          .where({
            teacherId: userId,
            type: type
          })
          .count();
        typeStats[type] = result.total || 0;
      }

      return typeStats;
    } catch (error) {
      console.error('获取类型统计失败:', error);
      return {};
    }
  }

  /**
   * 保存评语到云数据库
   */
  async saveComment(commentData) {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      // 生产环境：隐藏评语数据内容

      // 如果有自定义ID，使用doc().set()保存，否则使用add()
      let result;
      if (commentData.id) {
        result = await db.collection('comments').doc(commentData.id).set({
          data: {
            ...commentData,
            teacherId: userId,
            createTime: new Date(),
            updateTime: new Date()
          }
        });
        // 保存成功
      } else {
        result = await db.collection('comments').add({
          data: {
            ...commentData,
            teacherId: userId,
            createTime: new Date(),
            updateTime: new Date()
          }
        });
        console.log('使用自动ID保存评语成功:', result);
      }

      return { success: true, data: result };
    } catch (error) {
      console.error('保存评语失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 批量保存评语
   */
  async batchSaveComments(commentsArray) {
    try {
      console.log('批量保存评语:', commentsArray.length, '条');
      const results = [];
      
      for (const commentData of commentsArray) {
        const result = await this.saveComment(commentData);
        results.push(result);
        if (!result.success) {
          console.error('保存单条评语失败:', result.error);
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      return {
        success: failCount === 0,
        data: results,
        message: `成功保存 ${successCount} 条评语${failCount > 0 ? `，失败 ${failCount} 条` : ''}`
      };
    } catch (error) {
      console.error('批量保存评语失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 创建测试评语数据（生产环境已禁用）
   */
  async createTestComments() {
    // 生产环境不创建测试数据
    return { success: false, message: '生产环境已禁用测试数据创建' };
  }

  // ==================== 徽章系统云端接口 ====================

  /**
   * 获取用户徽章数据
   */
  async getUserAchievements() {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      console.log('[CloudService] 获取用户徽章数据:', userId);

      const result = await db.collection('user_achievements')
        .where({
          userId: userId
        })
        .get();

      if (result.data.length > 0) {
        const achievementData = result.data[0];
        console.log('[CloudService] 获取徽章数据成功:', achievementData.achievements?.length || 0, '个徽章');
        return {
          success: true,
          data: {
            achievements: achievementData.achievements || [],
            lastUpdated: achievementData.lastUpdated,
            version: achievementData.version || 1
          }
        };
      } else {
        // 用户首次使用，创建空的徽章记录
        console.log('[CloudService] 用户首次使用，创建空徽章记录');
        const initialData = {
          userId: userId,
          achievements: [],
          lastUpdated: Date.now(),
          version: 1,
          createTime: Date.now()
        };

        await db.collection('user_achievements').add({
          data: initialData
        });

        return {
          success: true,
          data: {
            achievements: [],
            lastUpdated: initialData.lastUpdated,
            version: 1
          }
        };
      }
    } catch (error) {
      console.error('[CloudService] 获取用户徽章数据失败:', error);
      return {
        success: false,
        error: error.message,
        data: { achievements: [], lastUpdated: 0, version: 1 }
      };
    }
  }

  /**
   * 更新用户徽章数据
   */
  async updateUserAchievements(achievements) {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      console.log('[CloudService] 更新用户徽章数据:', achievements.length, '个徽章');

      const updateData = {
        achievements: achievements,
        lastUpdated: Date.now(),
        version: Date.now() // 使用时间戳作为版本号
      };

      // 尝试更新现有记录
      const result = await db.collection('user_achievements')
        .where({
          userId: userId
        })
        .update({
          data: updateData
        });

      if (result.stats.updated > 0) {
        console.log('[CloudService] 徽章数据更新成功');
        return { success: true, data: updateData };
      } else {
        // 记录不存在，创建新记录
        console.log('[CloudService] 记录不存在，创建新徽章记录');
        await db.collection('user_achievements').add({
          data: {
            userId: userId,
            ...updateData,
            createTime: Date.now()
          }
        });
        return { success: true, data: updateData };
      }
    } catch (error) {
      console.error('[CloudService] 更新用户徽章数据失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 解锁单个徽章
   */
  async unlockAchievement(achievementId, achievementData) {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      console.log('[CloudService] 解锁徽章:', achievementId);

      // 获取当前徽章数据
      const currentResult = await this.getUserAchievements();
      if (!currentResult.success) {
        throw new Error('获取当前徽章数据失败');
      }

      const currentAchievements = currentResult.data.achievements || [];

      // 检查徽章是否已解锁
      const existingIndex = currentAchievements.findIndex(a => a.id === achievementId);

      if (existingIndex >= 0) {
        console.log('[CloudService] 徽章已解锁，更新数据');
        // 更新现有徽章数据
        currentAchievements[existingIndex] = {
          ...currentAchievements[existingIndex],
          ...achievementData,
          lastUpdated: Date.now()
        };
      } else {
        console.log('[CloudService] 新解锁徽章');
        // 添加新徽章
        currentAchievements.push({
          id: achievementId,
          unlockedAt: Date.now(),
          ...achievementData
        });
      }

      // 更新云端数据
      const updateResult = await this.updateUserAchievements(currentAchievements);

      if (updateResult.success) {
        console.log('[CloudService] 徽章解锁成功');
        return {
          success: true,
          data: {
            achievementId,
            achievements: currentAchievements
          }
        };
      } else {
        throw new Error(updateResult.error);
      }
    } catch (error) {
      console.error('[CloudService] 解锁徽章失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 清空用户徽章数据
   */
  async clearUserAchievements() {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      console.log('[CloudService] 清空用户徽章数据:', userId);

      // 删除用户的徽章记录
      const result = await db.collection('user_achievements')
        .where({
          userId: userId
        })
        .remove();

      console.log('[CloudService] 徽章数据清空成功，删除记录数:', result.stats.removed);

      return {
        success: true,
        data: {
          deletedCount: result.stats.removed,
          message: `成功清空 ${result.stats.removed} 条徽章记录`
        }
      };
    } catch (error) {
      console.error('[CloudService] 清空用户徽章数据失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 同步徽章数据（处理本地和云端数据冲突）
   */
  async syncAchievements(localAchievements, localVersion = 0) {
    try {
      const userId = await this.getCurrentUserId();
      console.log('[CloudService] 同步徽章数据，本地版本:', localVersion);

      // 获取云端数据
      const cloudResult = await this.getUserAchievements();
      if (!cloudResult.success) {
        throw new Error('获取云端徽章数据失败');
      }

      const cloudAchievements = cloudResult.data.achievements || [];
      const cloudVersion = cloudResult.data.version || 0;

      console.log('[CloudService] 云端版本:', cloudVersion, '本地版本:', localVersion);

      // 版本比较和冲突解决
      if (cloudVersion > localVersion) {
        // 云端数据更新，使用云端数据
        console.log('[CloudService] 使用云端数据（云端更新）');
        return {
          success: true,
          data: {
            achievements: cloudAchievements,
            version: cloudVersion,
            source: 'cloud',
            message: '已同步云端最新数据'
          }
        };
      } else if (localVersion > cloudVersion) {
        // 本地数据更新，上传到云端
        console.log('[CloudService] 上传本地数据到云端（本地更新）');
        const updateResult = await this.updateUserAchievements(localAchievements);

        if (updateResult.success) {
          return {
            success: true,
            data: {
              achievements: localAchievements,
              version: updateResult.data.version,
              source: 'local',
              message: '已上传本地数据到云端'
            }
          };
        } else {
          throw new Error('上传本地数据失败');
        }
      } else {
        // 版本相同，数据已同步
        console.log('[CloudService] 数据已同步');
        return {
          success: true,
          data: {
            achievements: cloudAchievements,
            version: cloudVersion,
            source: 'synced',
            message: '数据已同步'
          }
        };
      }
    } catch (error) {
      console.error('[CloudService] 同步徽章数据失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取评语列表
   */
  async getCommentList(options = {}) {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      // 查询评语列表

      let query = db.collection('comments').where({
        teacherId: userId
      });

      // 关键词搜索
      if (options.keyword) {
        query = query.where({
          $or: [
            { studentName: db.command.regex({ regexp: options.keyword, options: 'i' }) },
            { content: db.command.regex({ regexp: options.keyword, options: 'i' }) },
            { className: db.command.regex({ regexp: options.keyword, options: 'i' }) }
          ]
        });
      }

      // 时间范围筛选
      if (options.startDate && options.endDate) {
        query = query.where({
          createTime: db.command.gte(new Date(options.startDate))
            .and(db.command.lte(new Date(options.endDate)))
        });
      } else if (options.startDate) {
        query = query.where({
          createTime: db.command.gte(new Date(options.startDate))
        });
      } else if (options.endDate) {
        query = query.where({
          createTime: db.command.lte(new Date(options.endDate))
        });
      }

      // 筛选条件
      if (options.filter) {
        if (options.filter.style) {
          query = query.where({ style: options.filter.style });
        }
        if (options.filter.classId) {
          query = query.where({ className: options.filter.classId });
        }
      }

      // 分页和排序
      const page = options.page || 1;
      const limit = options.limit || 20;
      const skip = (page - 1) * limit;

      const result = await query
        .orderBy('createTime', 'desc')
        .skip(skip)
        .limit(limit)
        .get();

      console.log('评语列表查询结果:', result.data.length, '条');

      return { 
        success: true, 
        data: {
          comments: result.data,
          total: result.data.length,
          hasMore: result.data.length === limit
        }
      };
    } catch (error) {
      console.error('获取评语列表失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取评语详情
   */
  async getCommentDetail(commentId) {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      // 查询评语详情

      // 先检查数据库中是否有任何评语数据
      // 生产环境：不显示数据库内容

      const result = await db.collection('comments')
        .where({
          _id: commentId,
          teacherId: userId
        })
        .get();

      // 查询完成

      if (result.data.length === 0) {
        // 尝试不使用teacherId限制，看是否是权限问题
        const resultWithoutAuth = await db.collection('comments')
          .where({
            _id: commentId
          })
          .get();
        
        console.log('无权限检查的查询结果:', resultWithoutAuth);
        
        if (resultWithoutAuth.data.length === 0) {
          return { success: false, error: '评语不存在，请检查数据库中是否有测试数据' };
        } else {
          return { success: false, error: `无权限访问此评语，数据归属用户：${resultWithoutAuth.data[0].teacherId}，当前用户：${userId}` };
        }
      }

      return { success: true, data: result.data[0] };
    } catch (error) {
      console.error('获取评语详情失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 更新评语
   */
  async updateComment(commentId, updateData) {
    try {
      const db = await this.ensureDbInitialized();

      const result = await db.collection('comments').doc(commentId).update({
        data: {
          ...updateData,
          updateTime: new Date()
        }
      });

      return { success: true, data: result };
    } catch (error) {
      console.error('更新评语失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 删除评语
   */
  async deleteComment(commentId) {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      // 执行删除操作

      // 先检查评语是否存在且属于当前用户
      const checkResult = await db.collection('comments')
        .where({
          _id: commentId,
          teacherId: userId
        })
        .get();

      // 权限检查完成

      if (checkResult.data.length === 0) {
        // 尝试不使用teacherId限制，看是否是权限问题
        const resultWithoutAuth = await db.collection('comments')
          .where({
            _id: commentId
          })
          .get();
        
        if (resultWithoutAuth.data.length === 0) {
          return { success: false, error: '评语不存在' };
        } else {
          return { success: false, error: '无权限删除此评语' };
        }
      }

      // 删除评语
      console.log('开始删除评语...');
      const result = await db.collection('comments').doc(commentId).remove();
      console.log('删除结果:', result);
      
      return { success: true, data: result };
    } catch (error) {
      console.error('删除评语失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 清空所有评语
   */
  async clearAllComments() {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      // 先获取当前用户的所有评语数量
      const countResult = await db.collection('comments')
        .where({
          teacherId: userId
        })
        .count();

      if (countResult.total === 0) {
        return { success: true, message: '没有评语需要清空', deletedCount: 0 };
      }

      console.log(`准备清空 ${countResult.total} 条评语`);

      // 分批删除所有评语（微信云开发单次删除限制20条）
      let deletedCount = 0;
      const batchSize = 20;
      
      while (true) {
        // 获取一批评语
        const batchResult = await db.collection('comments')
          .where({
            teacherId: userId
          })
          .limit(batchSize)
          .get();

        if (batchResult.data.length === 0) {
          break; // 没有更多评语了
        }

        // 批量删除这批评语
        const deletePromises = batchResult.data.map(comment => 
          db.collection('comments').doc(comment._id).remove()
        );

        await Promise.all(deletePromises);
        deletedCount += batchResult.data.length;

        console.log(`已删除 ${deletedCount} 条评语`);
      }

      return { 
        success: true, 
        message: `成功清空所有评语`, 
        deletedCount 
      };
    } catch (error) {
      console.error('清空评语失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取学生的行为记录
   */
  async getStudentRecords(studentId, options = {}) {
    try {
      const userId = await this.getCurrentUserId();
      const db = await this.ensureDbInitialized();

      let query = db.collection('records').where({
        teacherId: userId,
        studentId: studentId
      });

      // 时间范围筛选
      if (options.startDate && options.endDate) {
        query = query.where({
          createTime: db.command.gte(new Date(options.startDate))
            .and(db.command.lte(new Date(options.endDate)))
        });
      }

      const result = await query
        .orderBy('createTime', 'desc')
        .limit(50)
        .get();

      // 处理字段名兼容，确保content和description字段统一
      const processedData = result.data.map(record => ({
        ...record,
        content: record.content || record.description || '',
        description: record.description || record.content || '',
        type: record.type || record.behaviorType || 'general',
        behaviorType: record.behaviorType || record.type || 'general'
      }));

      console.log(`[学生记录查询] 学生ID: ${studentId}, 查询到记录数量: ${processedData.length}`);
      if (processedData.length > 0) {
        console.log(`[学生记录查询] 第一条记录内容: ${processedData[0].content?.substring(0, 50)}...`);
      }

      return { success: true, data: processedData };
    } catch (error) {
      console.error('获取学生记录失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 重新生成评语
   */
  async regenerateComment(commentId) {
    try {
      const db = await this.ensureDbInitialized();

      // 获取原评语信息
      const commentResult = await db.collection('comments').doc(commentId).get();
      if (!commentResult.data) {
        throw new Error('评语不存在');
      }

      const originalComment = commentResult.data;

      // 获取学生记录
      const recordsResult = await this.getStudentRecords(originalComment.studentId, {
        startDate: originalComment.generateOptions?.startDate,
        endDate: originalComment.generateOptions?.endDate
      });

      // 这里应该调用AI服务重新生成
      // 暂时返回模拟数据
      const newContent = `重新生成的评语内容 - ${new Date().toLocaleString()}`;

      // 更新评语
      const updateResult = await this.updateComment(commentId, {
        content: newContent,
        regenerateTime: new Date()
      });

      if (updateResult.success) {
        return {
          success: true,
          data: {
            ...originalComment,
            content: newContent,
            regenerateTime: new Date()
          }
        };
      } else {
        throw new Error(updateResult.error);
      }

    } catch (error) {
      console.error('重新生成评语失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 批量删除评语
   */
  async batchDeleteComments(commentIds) {
    try {
      const db = await this.ensureDbInitialized();
      const batch = db.batch();

      commentIds.forEach(id => {
        batch.delete(db.collection('comments').doc(id));
      });

      const result = await batch.commit();
      return { success: true, data: result };
    } catch (error) {
      console.error('批量删除评语失败:', error);
      return { success: false, error: error.message };
    }
  }
}

// 创建全局实例
const cloudService = new CloudService();
cloudServiceInstance = cloudService;

console.log('cloudService 实例创建成功');

/**
 * 获取云服务实例的全局函数
 * 这个函数可以在任何地方调用来获取云服务实例
 */
function getCloudService() {
  if (!cloudServiceInstance) {
    console.warn('云服务实例尚未初始化，创建新实例');
    cloudServiceInstance = new CloudService();
  }
  return cloudServiceInstance;
}

// 将函数挂载到全局
if (typeof global !== 'undefined') {
  global.getCloudService = getCloudService;
}

// 导出模块
module.exports = {
  cloudService,
  CloudService,
  getCloudService
};

console.log('cloudService.js 模块导出完成');
