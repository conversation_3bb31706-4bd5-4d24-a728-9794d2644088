{"pages": ["pages/index/index", "pages/login/login", "pages/comment/generate/generate", "pages/comment/preview/preview", "pages/comment/list/list", "pages/comment/edit/edit", "pages/comment/detail/detail", "pages/settings/settings", "pages/record/create/create", "pages/record/list/list", "pages/record/detail/detail", "pages/profile/profile", "pages/student/list/list", "pages/student/create/create", "pages/student/detail/detail", "pages/class/list/list", "pages/class/create/create", "pages/class/detail/detail", "pages/analytics/overview/overview", "pages/analytics/class/class", "pages/analytics/student/student", "pages/analytics/growth/growth", "pages/analytics/report/report", "pages/works/list/list", "pages/works/edit/edit", "pages/debug-ai-database/index"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#5470C6", "navigationBarTitleText": "评语灵感君", "navigationBarTextStyle": "white", "backgroundColor": "#F0F2F5", "enablePullDownRefresh": true, "onReachBottomDistance": 50}, "tabBar": {"custom": false, "color": "#909399", "selectedColor": "#5470C6", "backgroundColor": "#ffffff", "borderStyle": "white", "list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/record/create/create", "text": "记录"}, {"pagePath": "pages/works/list/list", "text": "作品"}, {"pagePath": "pages/settings/settings", "text": "设置"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "debug": false, "sitemapLocation": "sitemap.json", "style": "v2", "useExtendedLib": {"weui": true}, "lazyCodeLoading": "requiredComponents"}