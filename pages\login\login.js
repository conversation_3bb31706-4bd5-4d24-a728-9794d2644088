/**
 * 登录页面
 */
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 弹窗状态
    showAgreement: false,
    showPrivacy: false,
    
    // 登录状态
    isLogging: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('登录页面加载');
    
    // 检查是否已经登录
    this.checkLoginStatus();
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    
    if (userInfo && token) {
      // 已登录，直接跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      // 如果是第一个页面，跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  /**
   * 微信登录
   */
  async wxLogin() {
    if (this.data.isLogging) {
      return;
    }

    try {
      // 首先检查用户是否同意服务协议
      const agreed = await this.checkUserAgreement();
      if (!agreed) {
        return;
      }

      this.setData({ isLogging: true });

      // 先获取用户信息 - 必须在用户点击事件中同步调用
      const userProfile = await this.getUserProfile();
      console.log('用户信息:', userProfile);

      wx.showLoading({
        title: '登录中...',
        mask: true
      });

      // 获取微信登录凭证
      const loginRes = await this.getWxLoginCode();
      console.log('微信登录凭证:', loginRes.code);

      // 模拟服务器登录验证
      const loginResult = await this.serverLogin({
        code: loginRes.code,
        userInfo: userProfile.userInfo
      });

      // 保存登录信息
      wx.setStorageSync('userInfo', loginResult.userInfo);
      wx.setStorageSync('token', loginResult.token);

      // 记录用户已同意协议（本地存储）
      wx.setStorageSync('userAgreementAccepted', true);
      wx.setStorageSync('agreementAcceptTime', new Date().toISOString());

      // 记录用户同意状态到数据库（法律合规）
      await this.recordUserConsentToDatabase(loginResult.userInfo, userProfile.userInfo);

      wx.hideLoading();

      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      });

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }, 1500);

    } catch (error) {
      wx.hideLoading();

      console.error('登录失败:', error);

      let errorMessage = '登录失败，请重试';
      if (error.errMsg) {
        if (error.errMsg.includes('cancel')) {
          errorMessage = '登录已取消';
        } else if (error.errMsg.includes('getUserProfile:fail')) {
          errorMessage = '获取用户信息失败';
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ isLogging: false });
    }
  },

  /**
   * 检查用户协议同意状态
   */
  checkUserAgreement() {
    return new Promise((resolve) => {
      // 检查是否已经同意过协议
      const agreed = wx.getStorageSync('userAgreementAccepted');
      if (agreed) {
        resolve(true);
        return;
      }

      // 显示用户协议同意弹窗
      wx.showModal({
        title: '📄 服务协议与隐私政策',
        content: `欢迎使用评语灵感君！

在使用我们的服务前，请您仔细阅读并同意：

🔒 《隐私政策》
了解我们如何收集、使用和保护您的信息

📄 《用户服务协议》
了解您的权利义务和服务使用条款

💡 我们承诺：
• 保护您的隐私安全
• 数据仅用于提供服务功能
• 您可随时查看、修改或删除数据

点击"同意并继续"即表示您已阅读并同意上述协议。`,
        confirmText: '同意并继续',
        cancelText: '查看详情',
        success: (res) => {
          if (res.confirm) {
            resolve(true);
          } else {
            // 用户点击查看详情，显示详细协议选择
            this.showAgreementDetails().then((agreed) => {
              resolve(agreed);
            });
          }
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  /**
   * 显示协议详情选择
   */
  showAgreementDetails() {
    return new Promise((resolve) => {
      wx.showActionSheet({
        itemList: ['查看隐私政策', '查看用户协议', '我已了解，同意协议'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 显示隐私政策
            this.showPrivacyPolicyDetail().then(() => {
              this.showAgreementDetails().then(resolve);
            });
          } else if (res.tapIndex === 1) {
            // 显示用户协议
            this.showUserAgreementDetail().then(() => {
              this.showAgreementDetails().then(resolve);
            });
          } else if (res.tapIndex === 2) {
            // 用户同意
            resolve(true);
          }
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  /**
   * 显示隐私政策详情
   */
  showPrivacyPolicyDetail() {
    return new Promise((resolve) => {
      wx.showModal({
        title: '🔒 隐私政策',
        content: `━━━━━━━━━━━━━━━━━━━━━━━━
📋 我们承诺保护您的隐私

🔍 信息收集：
• 基本账户信息（微信昵称、头像）
• 使用数据（学生档案、评语记录）
• 设备信息（设备型号、系统版本）

🔐 信息保护：
• 采用XOR + Base64双重加密
• 严格的访问权限控制机制
• 定期安全审计和漏洞扫描
• 本地离线加密处理

🤝 信息使用：
• 仅用于提供服务功能
• 不向第三方分享您的数据
• 不用于商业推广或营销
• 明确告知数据收集目的

👤 您的权利：
• 随时查看和修改个人信息
• 随时导出和删除您的数据
• 完全控制个人信息的使用
• 撤回授权同意

🔒 数据保存：
• 账户数据：使用期间保存
• 使用记录：保存不超过2年
• 注销后数据将被完全删除

📞 联系我们：
<EMAIL>
━━━━━━━━━━━━━━━━━━━━━━━━`,
        showCancel: false,
        confirmText: '我已了解',
        success: () => resolve()
      });
    });
  },

  /**
   * 显示用户协议详情
   */
  showUserAgreementDetail() {
    return new Promise((resolve) => {
      wx.showModal({
        title: '📄 用户服务协议',
        content: `━━━━━━━━━━━━━━━━━━━━━━━━
📋 服务协议要点

🎯 服务内容：
• AI智能评语生成服务
• 学生档案管理功能
• 数据备份与同步服务
• 使用统计分析功能

👤 您的权利：
• 正常使用所有服务功能
• 对创建数据享有所有权
• 个人信息受到保护
• 获得必要的技术支持

📜 您的义务：
• 遵守国家法律法规
• 合法合规使用服务
• 保护账户和密码安全
• 对输入内容承担责任

⚖️ 服务条款：
• 目前提供免费服务
• 保障服务稳定可靠
• 持续改进用户体验
• 提供必要技术支持

🚫 禁止行为：
• 违反法律法规的行为
• 传播有害内容
• 恶意攻击系统
• 商业滥用行为

📞 联系我们：
<EMAIL>
━━━━━━━━━━━━━━━━━━━━━━━━`,
        showCancel: false,
        confirmText: '我已了解',
        success: () => resolve()
      });
    });
  },

  /**
   * 记录用户同意状态到数据库（法律合规）
   */
  async recordUserConsentToDatabase(loginUserInfo, profileUserInfo) {
    try {
      console.log('[recordUserConsent] 开始记录用户同意状态到数据库...');

      // 获取设备信息
      const systemInfo = wx.getSystemInfoSync();

      // 准备同意记录数据
      const consentData = {
        userId: loginUserInfo.userId || wx.getStorageSync('userId'),
        userInfo: {
          nickName: profileUserInfo.nickName,
          avatarUrl: profileUserInfo.avatarUrl
        },
        consentType: 'both', // 同时同意隐私政策和用户协议
        consentVersion: '1.0',
        deviceInfo: {
          platform: systemInfo.platform,
          system: systemInfo.system,
          version: systemInfo.version,
          model: systemInfo.model,
          brand: systemInfo.brand,
          screenWidth: systemInfo.screenWidth,
          screenHeight: systemInfo.screenHeight,
          SDKVersion: systemInfo.SDKVersion
        },
        userAgent: `${systemInfo.platform}/${systemInfo.system} MiniProgram/${systemInfo.SDKVersion}`,
        // IP地址将由云函数自动获取
      };

      // 调用云函数记录同意状态
      const result = await wx.cloud.callFunction({
        name: 'recordUserConsent',
        data: consentData
      });

      if (result.result.success) {
        console.log('[recordUserConsent] 用户同意状态记录成功:', result.result);

        // 保存记录ID到本地，用于后续查询
        wx.setStorageSync('consentRecordId', result.result.recordId);
        wx.setStorageSync('consentRecordTime', result.result.consentTime);

        return true;
      } else {
        console.error('[recordUserConsent] 记录用户同意状态失败:', result.result.error);

        // 即使数据库记录失败，也不影响登录流程
        // 但需要记录错误，后续可以补录
        wx.setStorageSync('consentRecordPending', {
          data: consentData,
          error: result.result.error,
          timestamp: new Date().toISOString()
        });

        return false;
      }

    } catch (error) {
      console.error('[recordUserConsent] 记录用户同意状态异常:', error);

      // 记录异常信息，后续可以补录
      wx.setStorageSync('consentRecordPending', {
        data: consentData,
        error: error.message,
        timestamp: new Date().toISOString()
      });

      return false;
    }
  },

  /**
   * 获取微信登录凭证
   */
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  /**
   * 获取用户信息
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      // 检查是否支持getUserProfile
      if (wx.getUserProfile) {
        wx.getUserProfile({
          desc: '用于完善会员资料',
          success: (res) => {
            console.log('获取用户信息成功:', res);
            resolve(res);
          },
          fail: (err) => {
            console.error('获取用户信息失败:', err);
            // 如果用户拒绝授权，使用默认信息
            if (err.errMsg.includes('cancel') || err.errMsg.includes('deny')) {
              resolve({
                userInfo: {
                  nickName: '用户',
                  avatarUrl: '',
                  gender: 0,
                  city: '',
                  province: '',
                  country: ''
                }
              });
            } else {
              reject(err);
            }
          }
        });
      } else {
        // 降级处理：使用默认用户信息
        console.log('不支持getUserProfile，使用默认信息');
        resolve({
          userInfo: {
            nickName: '微信用户',
            avatarUrl: '',
            gender: 0,
            city: '',
            province: '',
            country: ''
          }
        });
      }
    });
  },

  /**
   * 服务器登录验证
   */
  async serverLogin(params) {
    try {
      // 🔥 调用云函数进行真实登录并保存用户信息到数据库
      const loginResult = await wx.cloud.callFunction({
        name: 'login',
        data: {
          code: params.code,
          userInfo: params.userInfo
        }
      });

      if (loginResult.result && loginResult.result.success) {
        // 🔥 登录成功后，保存用户信息到云数据库
        const cloudService = app.globalData.cloudService;
        if (cloudService) {
          const saveResult = await cloudService.saveUserInfo({
            nickName: params.userInfo.nickName,
            avatarUrl: params.userInfo.avatarUrl,
            gender: params.userInfo.gender,
            city: params.userInfo.city,
            province: params.userInfo.province,
            country: params.userInfo.country,
            lastLoginTime: new Date()
          });
          console.log('📊 用户信息保存到云数据库结果:', saveResult);
        }

        return {
          userInfo: {
            id: loginResult.result.openid,
            openid: loginResult.result.openid,
            nickName: params.userInfo.nickName,
            avatarUrl: params.userInfo.avatarUrl,
            gender: params.userInfo.gender,
            city: params.userInfo.city,
            province: params.userInfo.province,
            country: params.userInfo.country
          },
          token: loginResult.result.openid,
          isNewUser: loginResult.result.isNewUser || false
        };
      } else {
        throw new Error(loginResult.result?.error || '登录失败');
      }
    } catch (error) {
      console.error('☁️ 云登录失败，使用离线模式:', error);
      
      // 🔥 如果云登录失败，仍然尝试保存用户信息到本地数据库
      try {
        const cloudService = app.globalData.cloudService;
        if (cloudService) {
          await cloudService.saveUserInfo({
            nickName: params.userInfo.nickName,
            avatarUrl: params.userInfo.avatarUrl,
            gender: params.userInfo.gender,
            city: params.userInfo.city,
            province: params.userInfo.province,
            country: params.userInfo.country,
            lastLoginTime: new Date()
          });
          console.log('📊 离线模式用户信息保存成功');
        }
      } catch (saveError) {
        console.error('💾 离线保存用户信息失败:', saveError);
      }

      // 返回本地模拟数据
      return {
        userInfo: {
          id: Date.now(),
          nickName: params.userInfo.nickName,
          avatarUrl: params.userInfo.avatarUrl,
          gender: params.userInfo.gender,
          city: params.userInfo.city,
          province: params.userInfo.province,
          country: params.userInfo.country
        },
        token: 'offline_token_' + Date.now(),
        isNewUser: false
      };
    }
  },

  /**
   * 游客模式进入
   */
  enterGuestMode() {
    console.log('用户选择游客模式');

    // 设置游客模式标识
    wx.setStorageSync('isGuestMode', true);
    wx.setStorageSync('guestModeTime', Date.now());

    wx.showToast({
      title: '进入体验模式',
      icon: 'success',
      duration: 1000
    });

    // 跳转到首页
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }, 1000);
  },

  /**
   * 显示用户协议
   */
  showUserAgreement() {
    this.setData({ showAgreement: true });
  },

  /**
   * 隐藏用户协议
   */
  hideAgreement() {
    this.setData({ showAgreement: false });
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    this.setData({ showPrivacy: true });
  },

  /**
   * 隐藏隐私政策
   */
  hidePrivacy() {
    this.setData({ showPrivacy: false });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '评语灵感君 - AI评语生成助手',
      path: '/pages/login/login',
      imageUrl: '/images/share-cover.jpg'
    };
  }
});
