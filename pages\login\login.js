/**
 * 登录页面
 */
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 弹窗状态
    showAgreement: false,
    showPrivacy: false,
    
    // 登录状态
    isLogging: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('登录页面加载');
    
    // 检查是否已经登录
    this.checkLoginStatus();
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    
    if (userInfo && token) {
      // 已登录，直接跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      // 如果是第一个页面，跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  /**
   * 微信登录
   */
  async wxLogin() {
    if (this.data.isLogging) {
      return;
    }

    try {
      this.setData({ isLogging: true });

      // 先获取用户信息 - 必须在用户点击事件中同步调用
      const userProfile = await this.getUserProfile();
      console.log('用户信息:', userProfile);

      wx.showLoading({
        title: '登录中...',
        mask: true
      });

      // 获取微信登录凭证
      const loginRes = await this.getWxLoginCode();
      console.log('微信登录凭证:', loginRes.code);

      // 模拟服务器登录验证
      const loginResult = await this.serverLogin({
        code: loginRes.code,
        userInfo: userProfile.userInfo
      });

      // 保存登录信息
      wx.setStorageSync('userInfo', loginResult.userInfo);
      wx.setStorageSync('token', loginResult.token);

      wx.hideLoading();

      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      });

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }, 1500);

    } catch (error) {
      wx.hideLoading();

      console.error('登录失败:', error);

      let errorMessage = '登录失败，请重试';
      if (error.errMsg) {
        if (error.errMsg.includes('cancel')) {
          errorMessage = '登录已取消';
        } else if (error.errMsg.includes('getUserProfile:fail')) {
          errorMessage = '获取用户信息失败';
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      });
    } finally {
      this.setData({ isLogging: false });
    }
  },

  /**
   * 获取微信登录凭证
   */
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  /**
   * 获取用户信息
   */
  getUserProfile() {
    return new Promise((resolve, reject) => {
      // 检查是否支持getUserProfile
      if (wx.getUserProfile) {
        wx.getUserProfile({
          desc: '用于完善会员资料',
          success: (res) => {
            console.log('获取用户信息成功:', res);
            resolve(res);
          },
          fail: (err) => {
            console.error('获取用户信息失败:', err);
            // 如果用户拒绝授权，使用默认信息
            if (err.errMsg.includes('cancel') || err.errMsg.includes('deny')) {
              resolve({
                userInfo: {
                  nickName: '用户',
                  avatarUrl: '',
                  gender: 0,
                  city: '',
                  province: '',
                  country: ''
                }
              });
            } else {
              reject(err);
            }
          }
        });
      } else {
        // 降级处理：使用默认用户信息
        console.log('不支持getUserProfile，使用默认信息');
        resolve({
          userInfo: {
            nickName: '微信用户',
            avatarUrl: '',
            gender: 0,
            city: '',
            province: '',
            country: ''
          }
        });
      }
    });
  },

  /**
   * 服务器登录验证
   */
  async serverLogin(params) {
    // 模拟服务器API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          userInfo: {
            id: Date.now(),
            nickName: params.userInfo.nickName,
            avatarUrl: params.userInfo.avatarUrl,
            gender: params.userInfo.gender,
            city: params.userInfo.city,
            province: params.userInfo.province,
            country: params.userInfo.country
          },
          token: 'mock_token_' + Date.now(),
          isNewUser: false
        });
      }, 1000);
    });
  },

  /**
   * 游客模式进入
   */
  enterGuestMode() {
    console.log('用户选择游客模式');

    // 设置游客模式标识
    wx.setStorageSync('isGuestMode', true);
    wx.setStorageSync('guestModeTime', Date.now());

    wx.showToast({
      title: '进入体验模式',
      icon: 'success',
      duration: 1000
    });

    // 跳转到首页
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }, 1000);
  },

  /**
   * 显示用户协议
   */
  showUserAgreement() {
    this.setData({ showAgreement: true });
  },

  /**
   * 隐藏用户协议
   */
  hideAgreement() {
    this.setData({ showAgreement: false });
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    this.setData({ showPrivacy: true });
  },

  /**
   * 隐藏隐私政策
   */
  hidePrivacy() {
    this.setData({ showPrivacy: false });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '评语灵感君 - AI评语生成助手',
      path: '/pages/login/login',
      imageUrl: '/images/share-cover.jpg'
    };
  }
});
