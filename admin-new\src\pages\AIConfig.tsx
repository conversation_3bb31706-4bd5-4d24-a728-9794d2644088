import React, { useState, useEffect } from 'react'
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Typography,
  Tabs,
  InputNumber,
  Switch,
  message,
  Table,
  Tag,
  Modal,
  notification
} from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, ExperimentOutlined, RobotOutlined } from '@ant-design/icons'
import { getPromptTemplates, updatePromptTemplate, createPromptTemplate, deletePromptTemplate } from '../services/authApi'
import '../utils/testTemplateEdit'

const { Title, Text } = Typography
const { TextArea } = Input

const AIConfig: React.FC = () => {
  const [form] = Form.useForm()
  const [modelForm] = Form.useForm()
  const [templateForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [testModalVisible, setTestModalVisible] = useState(false)
  const [addModelModalVisible, setAddModelModalVisible] = useState(false)
  const [addTemplateModalVisible, setAddTemplateModalVisible] = useState(false)
  const [editModelModalVisible, setEditModelModalVisible] = useState(false)
  const [editTemplateModalVisible, setEditTemplateModalVisible] = useState(false)
  const [currentEditingModel, setCurrentEditingModel] = useState<any>(null)
  const [currentEditingTemplate, setCurrentEditingTemplate] = useState<any>(null)
  
  // 动态数据状态
  const [promptTemplates, setPromptTemplates] = useState<any[]>([])
  const [templatesLoading, setTemplatesLoading] = useState(false)
  const [aiModels, setAiModels] = useState<any[]>([])
  const [modelsLoading, setModelsLoading] = useState(false)

  // 加载提示词模板数据
  const loadPromptTemplates = async () => {
    try {
      setTemplatesLoading(true)
      console.log('🔍 开始加载提示词模板...')
      
      const result = await getPromptTemplates()
      console.log('📥 getPromptTemplates 返回结果:', result)
      
      // 确保我们获取的是数组数据
      const templates = result?.data || []
      console.log('📋 解析的模板数据:', templates)
      console.log('📊 模板数量:', templates.length)
      
      setPromptTemplates(Array.isArray(templates) ? templates : [])
      
      if (templates.length > 0) {
        console.log('✅ 成功加载模板:', templates.length, '个')
      } else {
        console.log('⚠️ 没有获取到模板数据')
      }
    } catch (error) {
      console.error('❌ Load templates error:', error)
      notification.error({
        message: '加载提示词模板失败',
        description: '请检查网络连接和云函数状态'
      })
    } finally {
      setTemplatesLoading(false)
    }
  }

  // 加载AI模型数据（连接到云函数）
  const loadAiModels = async () => {
    try {
      setModelsLoading(true)

      // 尝试从云函数获取AI模型配置
      try {
        const response = await fetch('http://localhost:3000/admin', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'ai.getModels'
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.code === 200 && result.data) {
            setAiModels(result.data)
            return
          }
        }
      } catch (apiError) {
        console.warn('云函数获取AI模型失败，使用本地存储:', apiError)
      }

      // 如果云函数失败，使用本地存储作为备用
      const savedModels = localStorage.getItem('aiModels')
      if (savedModels) {
        const models = JSON.parse(savedModels)
        // 确保删除操作的持久性 - 不再自动恢复默认模型
        setAiModels(models)
      } else {
        // 只在首次访问时创建默认模型
        const isFirstVisit = !localStorage.getItem('aiModelsInitialized')
        if (isFirstVisit) {
          const defaultModels = [
            {
              id: '1',
              name: '豆包模型',
              provider: 'bytedance',
              model: 'doubao-pro-32k',
              status: 'active',
              apiKey: 'bce-v3-***',
              usage: 0,
              cost: 0
            },
            {
              id: '2',
              name: 'GPT-4',
              provider: 'openai',
              model: 'gpt-4-turbo',
              status: 'inactive',
              apiKey: 'sk-***',
              usage: 0,
              cost: 0
            }
          ]
          setAiModels(defaultModels)
          localStorage.setItem('aiModels', JSON.stringify(defaultModels))
          localStorage.setItem('aiModelsInitialized', 'true')
        } else {
          // 已经初始化过，设置为空数组（用户可能已删除所有模型）
          setAiModels([])
        }
      }
    } catch (error) {
      console.error('Load AI models error:', error)
      notification.error({
        message: '加载AI模型失败',
        description: '请检查网络连接或本地存储'
      })
      setAiModels([]) // 出错时设置为空数组
    } finally {
      setModelsLoading(false)
    }
  }

  // 🔥 加载数据库中的AI配置
  const loadDatabaseConfig = async () => {
    try {
      console.log('尝试从数据库加载AI配置...')
      
      // 方案1：通过本地代理获取
      try {
        const response = await fetch('http://localhost:3000/admin', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'ai.getConfig'
          })
        })
        
        if (response.ok) {
          const result = await response.json()
          if (result.code === 200 && result.data) {
            console.log('从数据库加载的AI配置:', result.data)
            
            // 将数据库配置同步到表单
            const config = result.data
            form.setFieldsValue({
              model: config.model,
              provider: config.provider,
              apiKey: config.apiKey,
              apiUrl: config.apiUrl,
              temperature: config.temperature || 0.7,
              maxTokens: config.maxTokens || 2000,
              topP: config.topP || 0.9,
              frequencyPenalty: config.frequencyPenalty || 0,
              presencePenalty: config.presencePenalty || 0,
              enableStream: config.enableStream || false,
              enableCache: config.enableCache || true,
              timeout: config.timeout || 30
            })
            
            // 同时保存到localStorage作为缓存
            localStorage.setItem('aiSystemConfig', JSON.stringify(config))
            
            notification.success({
              message: '配置加载成功',
              description: '已从数据库加载AI配置'
            })
            return
          }
        }
      } catch (proxyError) {
        console.warn('本地代理加载配置失败:', proxyError)
      }
      
      // 方案2：从localStorage加载
      const localConfig = localStorage.getItem('aiSystemConfig')
      if (localConfig) {
        const config = JSON.parse(localConfig)
        form.setFieldsValue(config)
        console.log('从本地存储加载AI配置')
      }
      
    } catch (error) {
      console.error('加载AI配置失败:', error)
    }
  }

  // 组件加载时获取数据
  useEffect(() => {
    loadPromptTemplates()
    loadAiModels()
    loadDatabaseConfig() // 🔥 添加数据库配置加载
  }, [])

  const onFinish = async (values: any) => {
    setLoading(true)
    try {
      console.log('保存AI配置:', values)

      // 获取当前激活的AI模型
      const activeModel = aiModels.find(model => model.status === 'active')
      if (!activeModel) {
        notification.error({
          message: '保存失败',
          description: '请先配置并激活一个AI模型'
        })
        return
      }

      // 构建AI配置数据
      const aiConfigData = {
        type: 'ai_config',
        status: 'active',
        model: activeModel.model,
        provider: activeModel.provider,
        apiKey: activeModel.apiKey,
        apiUrl: activeModel.baseUrl || // 优先使用用户自定义的API URL
          (activeModel.provider === 'bytedance'
            ? 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
            : activeModel.provider === 'openai'
            ? 'https://api.openai.com/v1/chat/completions'
            : 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'),
        temperature: values.temperature || 0.7,
        maxTokens: values.maxTokens || 2000,
        topP: values.topP || 0.9,
        frequencyPenalty: values.frequencyPenalty || 0,
        presencePenalty: values.presencePenalty || 0,
        enableStream: values.enableStream || false,
        enableCache: values.enableCache || true,
        timeout: values.timeout || 30,
        updateTime: new Date().toISOString()
      }

      // 保存到云函数数据库 - 优先使用本地代理，失败时直接调用云函数
      let saveSuccess = false
      
      try {
        console.log('尝试通过本地代理保存AI配置...')
        const response = await fetch('http://localhost:3000/admin', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'ai.saveConfig',
            ...aiConfigData
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.code === 200) {
            saveSuccess = true
            notification.success({
              message: 'AI配置保存成功',
              description: '配置已同步到生成评语系统，新配置将在下次生成评语时生效'
            })
          } else {
            throw new Error(result.message || '本地代理保存失败')
          }
        } else {
          throw new Error(`HTTP ${response.status}`)
        }
      } catch (apiError) {
        console.warn('本地代理保存失败，尝试直接调用云函数:', apiError)
        
        // 🔥 备用方案：直接调用云函数API（跨域请求）
        try {
          console.log('尝试直接调用云函数保存配置...')
          
          // 直接调用腾讯云函数的HTTP API
          const cloudResponse = await fetch('https://tcb-api.tencentcloudapi.com/web', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-WX-SOURCE': 'miniprogram'
            },
            body: JSON.stringify({
              action: 'callFunction',
              function_name: 'adminAPI',
              request_data: {
                action: 'ai.saveConfig',
                ...aiConfigData
              }
            })
          })

          if (cloudResponse.ok) {
            const cloudResult = await cloudResponse.json()
            if (cloudResult.code === 0) {
              saveSuccess = true
              // 同时保存到本地作为缓存
              localStorage.setItem('aiSystemConfig', JSON.stringify(aiConfigData))
              
              notification.success({
                message: 'AI配置保存成功',
                description: '通过云函数直接保存成功！配置已同步到云数据库。'
              })
            } else {
              throw new Error(cloudResult.message || '云函数调用失败')
            }
          } else {
            throw new Error(`云函数HTTP API调用失败: ${cloudResponse.status}`)
          }
          
        } catch (cloudError) {
          console.error('云函数直接调用也失败:', cloudError)
          
          // 最终降级方案：保存到本地并提供同步指导
          localStorage.setItem('aiSystemConfig', JSON.stringify(aiConfigData))
          
          notification.warning({
            message: '配置已保存到本地',
            description: `云端同步失败: ${cloudError.message}。配置已保存到本地，您可以：
1. 检查网络连接后重试
2. 联系管理员手动同步配置
3. 使用小程序管理界面修改配置`
          })
          
          // 提供详细的手动同步指导
          console.log('🔧 === AI配置同步指导 ===')
          console.log('方案1: 手动添加到云数据库')
          console.log('集合: system_config')
          console.log('数据:', JSON.stringify(aiConfigData, null, 2))
          console.log('')
          console.log('方案2: 通过微信开发者工具云数据库控制台')
          console.log('1. 打开微信开发者工具')
          console.log('2. 选择云开发-数据库')
          console.log('3. 找到system_config集合')
          console.log('4. 添加记录或更新现有记录')
          console.log('================================')
        }
      }

    } catch (error) {
      console.error('保存AI配置失败:', error)
      notification.error({
        message: '保存失败',
        description: error.message || '请检查网络连接并重试'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleTestModel = () => {
    setTestModalVisible(true)
  }

  const handleStartTest = async () => {
    try {
      setLoading(true)

      // 获取当前激活的AI模型
      const activeModel = aiModels.find(model => model.status === 'active')
      if (!activeModel) {
        notification.error({
          message: 'AI模型测试失败',
          description: '请先配置并激活一个AI模型'
        })
        return
      }

      // 调用云函数测试AI模型
      try {
        const response = await fetch('http://localhost:3000/admin', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'ai.testModel',
            id: activeModel.id,
            testPrompt: '请为一个活泼好动、数学成绩优秀但语文需要加强的小学生写一条评语。'
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.code === 200 && result.data.success) {
            notification.success({
              message: 'AI模型测试成功',
              description: `响应时间: ${result.data.responseTime}ms | 模型响应正常`,
              duration: 4
            })
          } else {
            throw new Error(result.message || 'AI测试失败')
          }
        } else {
          throw new Error(`HTTP ${response.status}`)
        }
      } catch (apiError) {
        console.warn('云函数测试失败，使用模拟测试:', apiError)

        // 如果云函数测试失败，使用模拟测试
        await new Promise(resolve => setTimeout(resolve, 1000))

        const testResult = {
          success: true,
          responseTime: Math.floor(Math.random() * 500) + 200,
          tokensUsed: Math.floor(Math.random() * 100) + 50,
          response: "该学生在本学期表现优异，学习态度认真，积极参与课堂讨论，数学成绩稳步提升。建议继续保持良好的学习习惯，在语文阅读理解方面可以加强练习。"
        }

        notification.success({
          message: 'AI模型测试成功（模拟）',
          description: `响应时间: ${testResult.responseTime}ms | Tokens消耗: ${testResult.tokensUsed}`,
          duration: 4
        })
      }

      setTestModalVisible(false)
    } catch (error) {
      console.error('AI模型测试失败:', error)
      notification.error({
        message: 'AI模型测试失败',
        description: error.message || '请检查配置参数和网络连接'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleAddModel = () => {
    setAddModelModalVisible(true)
  }

  const handleAddModelSubmit = async (values: any) => {
    try {
      setLoading(true)

      const newModel = {
        id: Date.now().toString(),
        name: values.modelName,
        provider: values.provider,
        model: values.modelVersion,
        status: values.enabled ? 'active' : 'inactive',
        apiKey: values.apiKey,
        baseUrl: values.baseUrl, // 保存用户输入的API基础URL
        usage: 0,
        cost: 0
      }

      // 先尝试保存到云函数
      try {
        const response = await fetch('http://localhost:3000/admin', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'ai.createModel',
            name: values.modelName,
            provider: values.provider,
            model: values.modelVersion,
            config: {
              apiKey: values.apiKey,
              baseUrl: values.baseUrl, // 传递自定义API URL
              temperature: 0.7,
              maxTokens: 2000
            }
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.code === 200) {
            // 使用云函数返回的ID
            newModel.id = result.data.id || newModel.id
          }
        }
      } catch (apiError) {
        console.warn('云函数保存失败，仅保存到本地:', apiError)
      }

      const updatedModels = [...aiModels, newModel]
      setAiModels(updatedModels)
      localStorage.setItem('aiModels', JSON.stringify(updatedModels))

      notification.success({
        message: '模型添加成功',
        description: `${values.modelName} 已成功添加到模型列表`
      })

      setAddModelModalVisible(false)
      modelForm.resetFields()

    } catch (error) {
      console.error('添加模型失败:', error)
      notification.error({
        message: '模型添加失败',
        description: '请检查配置信息并重试'
      })
    } finally {
      setLoading(false)
    }
  }

  // 编辑模型
  const handleEditModel = (model: any) => {
    setCurrentEditingModel(model)
    modelForm.setFieldsValue({
      modelName: model.name,
      provider: model.provider,
      modelVersion: model.model,
      apiKey: model.apiKey,
      baseUrl: model.baseUrl, // 加载用户之前设置的API基础URL
      enabled: model.status === 'active'
    })
    setEditModelModalVisible(true)
  }

  // 删除模型
  const handleDeleteModel = (model: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除模型 "${model.name}" 吗？此操作不可恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 先尝试从云函数删除
          try {
            const response = await fetch('http://localhost:3000/admin', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                action: 'ai.deleteModel',
                id: model.id
              })
            })

            if (response.ok) {
              const result = await response.json()
              if (result.code !== 200) {
                console.warn('云函数删除失败:', result.message)
              }
            }
          } catch (apiError) {
            console.warn('云函数删除请求失败:', apiError)
          }

          // 无论云函数是否成功，都更新本地状态
          const updatedModels = aiModels.filter(m => m.id !== model.id)
          setAiModels(updatedModels)
          localStorage.setItem('aiModels', JSON.stringify(updatedModels))

          notification.success({
            message: '删除成功',
            description: `模型 "${model.name}" 已成功删除`
          })
        } catch (error) {
          console.error('删除模型失败:', error)
          notification.error({
            message: '删除失败',
            description: '请重试或联系管理员'
          })
        }
      }
    })
  }

  // 添加模板
  const handleAddTemplate = () => {
    setAddTemplateModalVisible(true)
  }

  // 编辑模板
  const handleEditTemplate = (template: any) => {
    console.log('📝 编辑模板:', template)
    setCurrentEditingTemplate(template)
    
    const formValues = {
      templateName: template.name,
      templateType: template.type,
      templateContent: template.content,
      templateDescription: template.description,
      enabled: template.status === 'active' || template.enabled === true
    }
    
    console.log('📝 设置表单值:', formValues)
    templateForm.setFieldsValue(formValues)
    setEditTemplateModalVisible(true)
  }

  // 删除模板
  const handleDeleteTemplate = (template: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除模板 "${template.name}" 吗？此操作不可恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deletePromptTemplate(template.id)
          notification.success({
            message: '删除成功',
            description: `模板 "${template.name}" 已成功删除`
          })
          // 刷新模板数据
          await loadPromptTemplates()
        } catch (error) {
          console.error('Delete template error:', error)
          notification.error({
            message: '删除失败',
            description: '请检查网络连接或联系管理员'
          })
        }
      }
    })
  }

  // 提交模板表单
  const handleTemplateSubmit = async (values: any) => {
    const isEdit = currentEditingTemplate !== null
    try {
      setLoading(true)

      // 调用云函数API
      if (isEdit) {
        console.log('🔧 更新模板数据:', {
          id: currentEditingTemplate.id,
          name: values.templateName,
          type: values.templateType,
          content: values.templateContent,
          description: values.templateDescription,
          enabled: values.enabled
        })
        
        await updatePromptTemplate({
          id: currentEditingTemplate.id,
          name: values.templateName,
          type: values.templateType,
          content: values.templateContent,
          description: values.templateDescription,
          enabled: values.enabled
        })
      } else {
        await createPromptTemplate({
          name: values.templateName,
          type: values.templateType,
          content: values.templateContent,
          description: values.templateDescription,
          status: values.enabled ? 'active' : 'inactive'
        })
      }

      notification.success({
        message: isEdit ? '模板更新成功' : '模板添加成功',
        description: `${values.templateName} 已成功${isEdit ? '更新' : '添加'}`
      })

      setAddTemplateModalVisible(false)
      setEditTemplateModalVisible(false)
      templateForm.resetFields()
      setCurrentEditingTemplate(null)

      // 刷新模板数据
      console.log('🔄 开始重新加载模板数据...')
      await loadPromptTemplates()
      console.log('✅ 模板数据重新加载完成')

    } catch (error) {
      console.error('Template operation error:', error)
      notification.error({
        message: isEdit ? '模板更新失败' : '模板添加失败',
        description: '请检查网络连接和配置信息'
      })
    } finally {
      setLoading(false)
    }
  }

  // 提交编辑模型表单
  const handleEditModelSubmit = async (values: any) => {
    try {
      setLoading(true)

      // 先尝试更新云函数中的模型配置
      try {
        const response = await fetch('http://localhost:3000/admin', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'ai.updateModel',
            id: currentEditingModel.id,
            name: values.modelName,
            provider: values.provider,
            model: values.modelVersion,
            status: values.enabled ? 'active' : 'inactive',
            config: {
              apiKey: values.apiKey,
              baseUrl: values.baseUrl, // 传递自定义API URL
              temperature: 0.7,
              maxTokens: 2000
            }
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.code !== 200) {
            console.warn('云函数更新失败:', result.message)
          }
        }
      } catch (apiError) {
        console.warn('云函数更新请求失败:', apiError)
      }

      // 更新本地状态
      const updatedModels = aiModels.map(model =>
        model.id === currentEditingModel.id
          ? {
              ...model,
              name: values.modelName,
              provider: values.provider,
              model: values.modelVersion,
              status: values.enabled ? 'active' : 'inactive',
              apiKey: values.apiKey,
              baseUrl: values.baseUrl // 保存用户输入的API基础URL
            }
          : model
      )

      setAiModels(updatedModels)
      localStorage.setItem('aiModels', JSON.stringify(updatedModels))

      notification.success({
        message: '模型更新成功',
        description: `${values.modelName} 已成功更新`
      })

      setEditModelModalVisible(false)
      modelForm.resetFields()
      setCurrentEditingModel(null)

    } catch (error) {
      console.error('编辑模型失败:', error)
      notification.error({
        message: '模型更新失败',
        description: '请检查配置信息并重试'
      })
    } finally {
      setLoading(false)
    }
  }

  const modelColumns = [
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '提供商',
      dataIndex: 'provider',
      key: 'provider',
      render: (provider: string) => (
        <Tag color={provider === 'bytedance' ? 'blue' : 'green'}>
          {provider === 'bytedance' ? '字节跳动' : 'OpenAI'}
        </Tag>
      )
    },
    {
      title: '模型版本',
      dataIndex: 'model',
      key: 'model'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '启用' : '停用'}
        </Tag>
      )
    },
    {
      title: '使用次数',
      dataIndex: 'usage',
      key: 'usage'
    },
    {
      title: '费用(元)',
      dataIndex: 'cost',
      key: 'cost',
      render: (cost: number) => `¥${cost.toFixed(2)}`
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="text" icon={<EditOutlined />} size="small" onClick={() => handleEditModel(record)}>
            编辑
          </Button>
          <Button type="text" icon={<ExperimentOutlined />} size="small" onClick={handleTestModel}>
            测试
          </Button>
          <Button type="text" icon={<DeleteOutlined />} size="small" danger onClick={() => handleDeleteModel(record)}>
            删除
          </Button>
        </Space>
      )
    }
  ]

  const templateColumns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap: Record<string, { text: string; color: string }> = {
          gentle: { text: '温和亲切型', color: 'green' },
          encouraging: { text: '鼓励激励型', color: 'blue' },
          detailed: { text: '详细具体型', color: 'orange' },
          comprehensive: { text: '综合发展型', color: 'purple' },
          formal: { text: '正式规范型', color: 'cyan' }
        }
        const typeInfo = typeMap[type] || { text: type, color: 'default' }
        return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>
      }
    },
    {
      title: '使用次数',
      dataIndex: 'usage',
      key: 'usage'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '启用' : '停用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="text" icon={<EditOutlined />} size="small" onClick={() => handleEditTemplate(record)}>
            编辑
          </Button>
          <Button type="text" icon={<DeleteOutlined />} size="small" danger onClick={() => handleDeleteTemplate(record)}>
            删除
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6 transition-colors">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <Title level={1} className="!mb-2 theme-text-primary flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                <RobotOutlined className="text-2xl text-white" />
              </div>
              AI模型配置
            </Title>
            <Text className="theme-text-secondary text-lg">
              管理AI模型和提示词模板配置
            </Text>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold theme-text-primary mb-1">
              {new Date().toLocaleTimeString()}
            </div>
            <div className="theme-text-secondary">
              {new Date().toLocaleDateString('zh-CN', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long' 
              })}
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-6">

        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-xl transition-colors">
          <Tabs
            defaultActiveKey="models"
            type="card"
            items={[
              {
                key: 'models',
                label: 'AI模型配置',
                children: (
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text strong>当前AI模型</Text>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddModel}>
                  添加模型
                </Button>
              </div>
              
              <Table 
                columns={modelColumns} 
                dataSource={aiModels}
                rowKey="id"
                pagination={false}
                loading={modelsLoading}
              />
            </Space>
                )
              },
              {
                key: 'templates',
                label: '提示词模板',
                children: (
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text strong>提示词模板</Text>
                <Space>
                  <Button type="primary" icon={<PlusOutlined />} onClick={handleAddTemplate}>
                    添加模板
                  </Button>
                </Space>
              </div>
              
              <Table 
                columns={templateColumns} 
                dataSource={promptTemplates}
                rowKey="id"
                pagination={false}
                loading={templatesLoading}
              />
            </Space>
                )
              },
              {
                key: 'settings',
                label: '系统参数',
                children: (
            <Card title="AI系统参数配置">
              <Form
                form={form}
                layout="vertical"
                onFinish={onFinish}
                initialValues={{
                  temperature: 0.7,
                  maxTokens: 2000,
                  topP: 0.9,
                  frequencyPenalty: 0,
                  presencePenalty: 0,
                  enableStream: true,
                  enableCache: true,
                  timeout: 30
                }}
              >
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 16 }}>
                  <Form.Item
                    label="温度参数"
                    name="temperature"
                    tooltip="控制生成文本的随机性，值越高越随机"
                  >
                    <InputNumber min={0} max={2} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="最大Token数"
                    name="maxTokens"
                    tooltip="生成文本的最大长度"
                  >
                    <InputNumber min={100} max={4000} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="Top P"
                    name="topP"
                    tooltip="核采样参数，控制文本多样性"
                  >
                    <InputNumber min={0} max={1} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="频率惩罚"
                    name="frequencyPenalty"
                    tooltip="降低重复内容的概率"
                  >
                    <InputNumber min={-2} max={2} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="存在惩罚"
                    name="presencePenalty"
                    tooltip="鼓励生成新内容"
                  >
                    <InputNumber min={-2} max={2} step={0.1} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    label="超时时间(秒)"
                    name="timeout"
                    tooltip="API请求超时时间"
                  >
                    <InputNumber min={5} max={120} style={{ width: '100%' }} />
                  </Form.Item>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16, marginTop: 16 }}>
                  <Form.Item
                    label="启用流式输出"
                    name="enableStream"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>

                  <Form.Item
                    label="启用缓存"
                    name="enableCache"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </div>

                <Form.Item style={{ marginTop: 24 }}>
                  <Space>
                    <Button type="primary" htmlType="submit" loading={loading}>
                      保存配置
                    </Button>
                    <Button onClick={() => form.resetFields()}>
                      重置
                    </Button>
                    <Button onClick={handleTestModel}>
                      测试连接
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
                )
              }
            ]}
          />
        </div>
      </div>

      {/* 测试模型对话框 */}
      <Modal
        title="测试AI模型"
        open={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setTestModalVisible(false)}>
            取消
          </Button>,
          <Button key="test" type="primary" loading={loading} onClick={handleStartTest}>
            开始测试
          </Button>
        ]}
      >
        <Form layout="vertical">
          <Form.Item label="测试输入">
            <TextArea
              rows={4}
              placeholder="请输入测试文本，用于验证AI模型响应..."
              defaultValue="请为一个活泼好动、数学成绩优秀但语文需要加强的小学生写一条评语。"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加模型对话框 */}
      <Modal
        title="添加AI模型"
        open={addModelModalVisible}
        onCancel={() => {
          setAddModelModalVisible(false)
          modelForm.resetFields()
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setAddModelModalVisible(false)
            modelForm.resetFields()
          }}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={loading} onClick={() => modelForm.submit()}>
            添加模型
          </Button>
        ]}
        width={600}
      >
        <Form
          form={modelForm}
          layout="vertical"
          onFinish={handleAddModelSubmit}
        >
          <Form.Item
            label="模型名称"
            name="modelName"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="例如：GPT-4 Turbo" />
          </Form.Item>

          <Form.Item
            label="提供商"
            name="provider"
            rules={[{ required: true, message: '请选择提供商' }]}
          >
            <Select placeholder="请选择AI模型提供商">
              <Select.Option value="openai">OpenAI</Select.Option>
              <Select.Option value="bytedance">字节跳动</Select.Option>
              <Select.Option value="anthropic">Anthropic</Select.Option>
              <Select.Option value="google">Google</Select.Option>
              <Select.Option value="baidu">百度</Select.Option>
              <Select.Option value="alibaba">阿里云</Select.Option>
              <Select.Option value="tencent">腾讯云</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="模型版本"
            name="modelVersion"
            rules={[{ required: true, message: '请输入模型版本' }]}
          >
            <Input placeholder="例如：gpt-4-turbo-preview" />
          </Form.Item>

          <Form.Item
            label="API密钥"
            name="apiKey"
            rules={[{ required: true, message: '请输入API密钥' }]}
          >
            <Input.Password placeholder="请输入API密钥" />
          </Form.Item>

          <Form.Item
            label={<span>API基础URL <span style={{ color: 'red' }}>*</span></span>}
            name="baseUrl"
            rules={[{ required: true, message: '请输入API基础URL' }]}
            tooltip="必填，API服务的基础地址"
          >
            <Input placeholder="例如：https://api.openai.com/v1" />
          </Form.Item>

          <Form.Item
            label="模型描述"
            name="description"
          >
            <TextArea
              rows={3}
              placeholder="请输入模型的简要描述..."
            />
          </Form.Item>

          <Form.Item
            label="启用状态"
            name="enabled"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="停用" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加模板对话框 */}
      <Modal
        title="添加提示词模板"
        open={addTemplateModalVisible}
        onCancel={() => {
          setAddTemplateModalVisible(false)
          templateForm.resetFields()
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setAddTemplateModalVisible(false)
            templateForm.resetFields()
          }}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={loading} onClick={() => templateForm.submit()}>
            添加模板
          </Button>
        ]}
        width={800}
      >
        <Form
          form={templateForm}
          layout="vertical"
          onFinish={handleTemplateSubmit}
        >
          <Form.Item
            label="模板名称"
            name="templateName"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="例如：积极鼓励型评语" />
          </Form.Item>

          <Form.Item
            label="模板类型"
            name="templateType"
            rules={[{ required: true, message: '请选择模板类型' }]}
          >
            <Select placeholder="请选择模板类型">
              <Select.Option value="gentle">温和亲切型</Select.Option>
              <Select.Option value="encouraging">鼓励激励型</Select.Option>
              <Select.Option value="detailed">详细具体型</Select.Option>
              <Select.Option value="comprehensive">综合发展型</Select.Option>
              <Select.Option value="formal">正式规范型</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="模板描述"
            name="templateDescription"
            rules={[{ required: true, message: '请输入模板描述' }]}
          >
            <Input placeholder="请简要描述此模板的用途和特点" />
          </Form.Item>

          <Form.Item
            label="模板内容"
            name="templateContent"
            rules={[{ required: true, message: '请输入模板内容' }]}
          >
            <TextArea
              rows={8}
              placeholder="请输入提示词模板内容，可以使用 {{变量名}} 的形式定义变量..."
            />
          </Form.Item>

          <Form.Item
            label="启用状态"
            name="enabled"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="停用" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑模板对话框 */}
      <Modal
        title="编辑提示词模板"
        open={editTemplateModalVisible}
        onCancel={() => {
          setEditTemplateModalVisible(false)
          templateForm.resetFields()
          setCurrentEditingTemplate(null)
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setEditTemplateModalVisible(false)
            templateForm.resetFields()
            setCurrentEditingTemplate(null)
          }}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={loading} onClick={() => templateForm.submit()}>
            更新模板
          </Button>
        ]}
        width={800}
      >
        <Form
          form={templateForm}
          layout="vertical"
          onFinish={handleTemplateSubmit}
        >
          <Form.Item
            label="模板名称"
            name="templateName"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="例如：积极鼓励型评语" />
          </Form.Item>

          <Form.Item
            label="模板类型"
            name="templateType"
            rules={[{ required: true, message: '请选择模板类型' }]}
          >
            <Select placeholder="请选择模板类型">
              <Select.Option value="gentle">温和亲切型</Select.Option>
              <Select.Option value="encouraging">鼓励激励型</Select.Option>
              <Select.Option value="detailed">详细具体型</Select.Option>
              <Select.Option value="comprehensive">综合发展型</Select.Option>
              <Select.Option value="formal">正式规范型</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="模板描述"
            name="templateDescription"
            rules={[{ required: true, message: '请输入模板描述' }]}
          >
            <Input placeholder="请简要描述此模板的用途和特点" />
          </Form.Item>

          <Form.Item
            label="模板内容"
            name="templateContent"
            rules={[{ required: true, message: '请输入模板内容' }]}
          >
            <TextArea
              rows={8}
              placeholder="请输入提示词模板内容，可以使用 {{变量名}} 的形式定义变量..."
            />
          </Form.Item>

          <Form.Item
            label="启用状态"
            name="enabled"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="停用" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑模型对话框 */}
      <Modal
        title="编辑AI模型"
        open={editModelModalVisible}
        onCancel={() => {
          setEditModelModalVisible(false)
          modelForm.resetFields()
          setCurrentEditingModel(null)
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setEditModelModalVisible(false)
            modelForm.resetFields()
            setCurrentEditingModel(null)
          }}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={loading} onClick={() => modelForm.submit()}>
            更新模型
          </Button>
        ]}
        width={600}
      >
        <Form
          form={modelForm}
          layout="vertical"
          onFinish={handleEditModelSubmit}
        >
          <Form.Item
            label="模型名称"
            name="modelName"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="例如：GPT-4 Turbo" />
          </Form.Item>

          <Form.Item
            label="提供商"
            name="provider"
            rules={[{ required: true, message: '请选择提供商' }]}
          >
            <Select placeholder="请选择AI模型提供商">
              <Select.Option value="openai">OpenAI</Select.Option>
              <Select.Option value="bytedance">字节跳动</Select.Option>
              <Select.Option value="anthropic">Anthropic</Select.Option>
              <Select.Option value="google">Google</Select.Option>
              <Select.Option value="baidu">百度</Select.Option>
              <Select.Option value="alibaba">阿里云</Select.Option>
              <Select.Option value="tencent">腾讯云</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="模型版本"
            name="modelVersion"
            rules={[{ required: true, message: '请输入模型版本' }]}
          >
            <Input placeholder="例如：gpt-4-turbo-preview" />
          </Form.Item>

          <Form.Item
            label="API密钥"
            name="apiKey"
            rules={[{ required: true, message: '请输入API密钥' }]}
          >
            <Input.Password placeholder="请输入API密钥" />
          </Form.Item>

          <Form.Item
            label={<span>API基础URL <span style={{ color: 'red' }}>*</span></span>}
            name="baseUrl"
            rules={[{ required: true, message: '请输入API基础URL' }]}
            tooltip="必填，API服务的基础地址"
          >
            <Input placeholder="例如：https://api.openai.com/v1" />
          </Form.Item>

          <Form.Item
            label="模型描述"
            name="description"
          >
            <TextArea
              rows={3}
              placeholder="请输入模型的简要描述..."
            />
          </Form.Item>

          <Form.Item
            label="启用状态"
            name="enabled"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="停用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default AIConfig