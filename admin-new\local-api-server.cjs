/**
 * 本地API服务器
 * 模拟小程序adminAPI云函数，用于测试管理后台连通性
 * 运行: node local-api-server.cjs
 */

const http = require('http')
const url = require('url')
// const databaseService = require('./database-service.js') // 暂时禁用

const PORT = 3000

// 配置选项
const USE_REAL_DATA = false // 暂时设置为false，使用模拟数据

// 模拟数据
const mockData = {
  dashboardStats: {
    totalUsers: 156,
    todayComments: 23,
    aiCalls: 89,
    satisfaction: 4.8,
    lastUpdated: new Date().toISOString()
  },
  recentActivities: [
    {
      id: '1',
      userId: 'user001',
      userName: '张老师',
      action: '生成评语',
      actionType: 'comment_generate',
      timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      metadata: { studentName: '小明', subject: '数学' }
    },
    {
      id: '2',
      userId: 'user002',
      userName: '李老师',
      action: '登录系统',
      actionType: 'user_login',
      timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
      metadata: {}
    }
  ],
  systemMetrics: {
    cpuUsage: 45.2,
    memoryUsage: 67.8,
    diskUsage: 23.1,
    networkLatency: 12,
    activeConnections: 8,
    lastUpdated: new Date().toISOString()
  },
  realtimeStats: {
    current: {
      todayComments: 23,
      todayRecords: 45,
      onlineUsers: 8,
      recentOperations: 12
    },
    system: {
      database: 'healthy',
      storage: 'healthy',
      functions: 'healthy'
    },
    trends: {
      hourly: Array.from({ length: 24 }, (_, i) => ({
        hour: i,
        timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        comments: Math.floor(Math.random() * 10) + 1,
        users: Math.floor(Math.random() * 5) + 1,
        operations: Math.floor(Math.random() * 15) + 5
      })),
      summary: {
        peakHour: 14,
        totalToday: 23
      }
    },
    lastUpdated: new Date().toISOString()
  },
  students: [
    {
      id: 'stu001',
      name: '张小明',
      class: '三年级一班',
      teacher: '王老师',
      commentsCount: 12,
      lastUpdate: new Date().toISOString()
    },
    {
      id: 'stu002',
      name: '李小红',
      class: '三年级一班',
      teacher: '王老师',
      commentsCount: 8,
      lastUpdate: new Date().toISOString()
    }
  ],
  aiModels: [
    {
      id: 'doubao',
      name: '豆包',
      provider: 'bytedance',
      status: 'active',
      config: {
        model: 'doubao-pro-4k',
        temperature: 0.7,
        maxTokens: 2000
      }
    }
  ]
}

// 处理API请求
async function handleApiRequest(req, res, body) {
  try {
    const data = JSON.parse(body)
    const { action, requestId } = data
    
    console.log(`📥 收到请求: ${action} (${requestId})`)
    
    let responseData = null
    
    switch (action) {
      case 'healthCheck':
        responseData = {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '3.0.0',
          source: data.source || 'unknown'
        }
        break
        
      case 'data.getDashboardStats':
        if (USE_REAL_DATA) {
          try {
            responseData = await databaseService.getDashboardStats()
            console.log('✅ 使用真实仪表板数据')
          } catch (error) {
            console.error('❌ 获取真实数据失败，使用模拟数据:', error)
            responseData = mockData.dashboardStats
          }
        } else {
          responseData = mockData.dashboardStats
        }
        break
        
      case 'data.getRecentActivities':
        if (USE_REAL_DATA) {
          try {
            responseData = await databaseService.getRecentActivities(data.limit || 10)
            console.log('✅ 使用真实活动数据')
          } catch (error) {
            console.error('❌ 获取真实活动数据失败，使用模拟数据:', error)
            responseData = mockData.recentActivities.slice(0, data.limit || 10)
          }
        } else {
          responseData = mockData.recentActivities.slice(0, data.limit || 10)
        }
        break
        
      case 'data.getSystemMetrics':
        responseData = mockData.systemMetrics
        break
        
      case 'realtime.getStats':
        responseData = mockData.realtimeStats
        break
        
      case 'data.getStudents':
        if (USE_REAL_DATA) {
          try {
            responseData = await databaseService.getStudents(data.params || {})
            console.log('✅ 使用真实学生数据')
          } catch (error) {
            console.error('❌ 获取真实学生数据失败，使用模拟数据:', error)
            responseData = {
              list: mockData.students,
              total: mockData.students.length,
              page: data.params?.page || 1,
              limit: data.params?.limit || 10
            }
          }
        } else {
          responseData = {
            list: mockData.students,
            total: mockData.students.length,
            page: data.params?.page || 1,
            limit: data.params?.limit || 10
          }
        }
        break

      case 'data.getRecords':
        if (USE_REAL_DATA) {
          try {
            responseData = await databaseService.getComments(data.params || {})
            console.log('✅ 使用真实评语数据')
          } catch (error) {
            console.error('❌ 获取真实评语数据失败，使用空数据:', error)
            responseData = {
              list: [],
              total: 0,
              page: data.params?.page || 1,
              limit: data.params?.limit || 10
            }
          }
        } else {
          responseData = {
            list: [],
            total: 0,
            page: data.params?.page || 1,
            limit: data.params?.limit || 10
          }
        }
        break

      case 'ai.getModels':
        responseData = mockData.aiModels
        break

      case 'ai.createModel':
        const newModel = {
          id: Date.now().toString(),
          name: data.name,
          provider: data.provider,
          model: data.model,
          status: 'active',
          config: data.config || {},
          createdAt: new Date().toISOString()
        }
        mockData.aiModels.push(newModel)
        responseData = {
          id: newModel.id,
          message: '模型配置创建成功',
          ...newModel
        }
        break

      case 'ai.updateModel':
        const modelIndex = mockData.aiModels.findIndex(m => m.id === data.id)
        if (modelIndex !== -1) {
          mockData.aiModels[modelIndex] = {
            ...mockData.aiModels[modelIndex],
            name: data.name || mockData.aiModels[modelIndex].name,
            provider: data.provider || mockData.aiModels[modelIndex].provider,
            model: data.model || mockData.aiModels[modelIndex].model,
            status: data.status || mockData.aiModels[modelIndex].status,
            config: data.config || mockData.aiModels[modelIndex].config,
            updatedAt: new Date().toISOString()
          }
          responseData = { message: '模型配置更新成功' }
        } else {
          throw new Error('模型配置不存在')
        }
        break

      case 'ai.deleteModel':
        const deleteIndex = mockData.aiModels.findIndex(m => m.id === data.id)
        if (deleteIndex !== -1) {
          mockData.aiModels.splice(deleteIndex, 1)
          responseData = { message: '模型配置删除成功' }
        } else {
          throw new Error('模型配置不存在')
        }
        break

      case 'ai.testModel':
        responseData = {
          success: true,
          response: '这是AI模型的测试回复。模型工作正常！',
          responseTime: Math.floor(Math.random() * 1000) + 500,
          tokens: {
            prompt: data.testPrompt?.length || 20,
            completion: 15,
            total: (data.testPrompt?.length || 20) + 15
          },
          timestamp: new Date().toISOString()
        }
        break

      case 'ai.saveConfig':
        // 模拟保存AI系统配置
        responseData = {
          id: 'config_' + Date.now(),
          message: 'AI配置保存成功，新配置已生效',
          config: data
        }
        console.log('💾 保存AI配置:', data)
        break

      case 'ai.getConfig':
        // 模拟获取AI配置
        responseData = {
          type: 'ai_config',
          status: 'active',
          model: 'doubao-pro-4k',
          provider: 'bytedance',
          apiKey: 'bce-v3-****',
          apiUrl: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
          temperature: 0.7,
          maxTokens: 2000,
          topP: 0.9,
          frequencyPenalty: 0,
          presencePenalty: 0,
          enableStream: false,
          enableCache: true,
          timeout: 30,
          updateTime: new Date().toISOString()
        }
        break

      case 'database.testConnection':
        if (USE_REAL_DATA) {
          try {
            responseData = await databaseService.testConnection()
            console.log('✅ 数据库连接测试完成')
          } catch (error) {
            console.error('❌ 数据库连接测试失败:', error)
            responseData = {
              success: false,
              message: error.message,
              collections: [],
              sampleData: null
            }
          }
        } else {
          responseData = {
            success: true,
            message: '使用模拟数据模式',
            collections: ['模拟数据'],
            sampleData: mockData.dashboardStats
          }
        }
        break

      default:
        throw new Error(`未知的action: ${action}`)
    }
    
    const response = {
      code: 200,
      message: 'success',
      data: responseData,
      requestId,
      timestamp: Date.now()
    }
    
    console.log(`✅ 响应: ${action} - ${JSON.stringify(response).length} bytes`)
    
    res.writeHead(200, {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE'
    })
    res.end(JSON.stringify(response))
    
  } catch (error) {
    console.error(`❌ 处理请求失败:`, error)
    
    const errorResponse = {
      code: 500,
      message: error.message,
      data: null,
      timestamp: Date.now()
    }
    
    res.writeHead(500, {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    })
    res.end(JSON.stringify(errorResponse))
  }
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true)
  
  // 处理CORS预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-WX-SOURCE'
    })
    res.end()
    return
  }
  
  // 只处理/admin路径的POST请求
  if (parsedUrl.pathname === '/admin' && req.method === 'POST') {
    let body = ''
    
    req.on('data', chunk => {
      body += chunk.toString()
    })
    
    req.on('end', async () => {
      await handleApiRequest(req, res, body)
    })
  } else {
    // 返回404
    res.writeHead(404, {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    })
    res.end(JSON.stringify({
      code: 404,
      message: 'Not Found',
      data: null
    }))
  }
})

// 启动服务器
server.listen(PORT, () => {
  console.log(`🚀 本地API服务器启动成功!`)
  console.log(`📡 服务地址: http://localhost:${PORT}/admin`)
  console.log(`🔗 测试地址: http://localhost:${PORT}/admin`)
  console.log(`📋 支持的actions:`)
  console.log(`   - healthCheck`)
  console.log(`   - data.getDashboardStats`)
  console.log(`   - data.getRecentActivities`)
  console.log(`   - data.getSystemMetrics`)
  console.log(`   - realtime.getStats`)
  console.log(`   - data.getStudents`)
  console.log(`   - ai.getModels`)
  console.log(`\n💡 在浏览器测试页面中可以测试连通性`)
  console.log(`\n按 Ctrl+C 停止服务器`)
})

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...')
  server.close(() => {
    console.log('✅ 服务器已关闭')
    process.exit(0)
  })
})
