/**
 * 模板缓存管理系统
 * Ultra-Think架构 - 多层缓存策略，确保模板获取的高可用性
 * 
 * 缓存层级：
 * Level 1: 内存缓存 (最快)
 * Level 2: 本地存储 (离线可用)
 * Level 3: 云端获取 (动态更新)
 * Level 4: 硬编码兜底 (100%可用)
 */

class TemplateCache {
  constructor() {
    this.cache = new Map()
    this.lastUpdate = 0
    this.CACHE_DURATION = 30 * 60 * 1000 // 30分钟
    this.LOCAL_STORAGE_KEY = 'template_cache_v2'
    this.VERSION_KEY = 'template_version_v2'
    this.initialized = false
  }

  /**
   * 获取模板（主要接口）
   */
  async getTemplate(type) {
    try {
      await this.ensureInitialized()
      
      // Level 1: 检查内存缓存
      if (this.cache.has(type) && !this.isExpired()) {
        console.log(`模板缓存命中 [内存]: ${type}`)
        return this.cache.get(type)
      }

      // Level 2: 检查本地存储
      const localTemplate = this.getFromLocalStorage(type)
      if (localTemplate && this.isLocalTemplateValid(localTemplate)) {
        console.log(`模板缓存命中 [本地]: ${type}`)
        this.cache.set(type, localTemplate)
        return localTemplate
      }

      // Level 3: 尝试从云端获取
      try {
        const cloudTemplate = await this.fetchFromCloud(type)
        if (cloudTemplate) {
          console.log(`模板获取成功 [云端]: ${type}`)
          this.cache.set(type, cloudTemplate)
          this.saveToLocalStorage(type, cloudTemplate)
          this.lastUpdate = Date.now()
          return cloudTemplate
        }
      } catch (cloudError) {
        console.warn(`云端获取模板失败 [${type}]:`, cloudError.message)
      }

      // Level 4: 降级到硬编码模板
      console.warn(`使用硬编码模板兜底 [${type}]`)
      const fallbackTemplate = this.getHardcodedTemplate(type)
      this.cache.set(type, fallbackTemplate)
      return fallbackTemplate

    } catch (error) {
      console.error(`模板获取失败 [${type}]:`, error)
      // 最后的保险：返回基础硬编码模板
      return this.getHardcodedTemplate(type)
    }
  }

  /**
   * 从云端获取模板 - 使用专用的getPromptTemplate云函数
   */
  async fetchFromCloud(type) {
    console.log(`[模板缓存] 调用getPromptTemplate云函数获取${type}类型模板`)
    
    const result = await wx.cloud.callFunction({
      name: 'getPromptTemplate',
      data: {
        type: type
      }
    })

    console.log(`[模板缓存] 云端返回原始数据:`, result)
    console.log(`[模板缓存] result.result:`, result.result)

    if (result.result) {
      const response = result.result
      
      // 处理getPromptTemplate的响应格式
      if (response.success === true && response.data) {
        // 成功响应：{ success: true, data: {...}, source: 'database|default|fallback' }
        const template = response.data
        console.log(`[模板缓存] 使用云函数返回的模板数据，来源: ${response.source}`)
        
        console.log(`[模板缓存] 解析后的模板数据:`, template)
        console.log(`[模板缓存] template.content类型:`, typeof template.content)
        console.log(`[模板缓存] template.content长度:`, template.content?.length)
        console.log(`[模板缓存] template.type:`, template.type)
        
        // 验证模板数据完整性
        if (this.validateCloudTemplate(template)) {
          console.log(`[模板缓存] 模板验证通过`)
          return {
            ...template,
            source: response.source || 'cloud',
            cachedAt: Date.now(),
            cacheVersion: 2
          }
        } else {
          console.error(`[模板缓存] 模板验证失败:`, {
            hasTemplate: !!template,
            hasContent: !!template.content,
            hasType: !!template.type,
            contentType: typeof template.content,
            contentLength: template.content?.length
          })
          throw new Error('云端模板数据格式无效')
        }
      } else if (response.success === false) {
        // 失败响应
        console.error(`[模板缓存] getPromptTemplate返回失败:`, response.error)
        throw new Error(response.error || 'getPromptTemplate调用失败')
      } else {
        console.error(`[模板缓存] getPromptTemplate返回格式异常:`, response)
        throw new Error('云函数返回数据格式异常')
      }
    } else {
      console.error(`[模板缓存] 云函数调用失败:`, result)
      throw new Error(result.errMsg || result.error || '云端模板获取失败')
    }
  }

  /**
   * 验证云端模板数据
   */
  validateCloudTemplate(template) {
    return template && 
           template.content && 
           template.type &&
           typeof template.content === 'string' &&
           template.content.length > 50
  }

  /**
   * 从本地存储获取模板
   */
  getFromLocalStorage(type) {
    try {
      const cacheData = wx.getStorageSync(this.LOCAL_STORAGE_KEY)
      if (cacheData && cacheData[type]) {
        const template = cacheData[type]
        
        // 检查本地缓存版本
        if (template.cacheVersion >= 2) {
          return template
        }
      }
    } catch (error) {
      console.warn('本地存储读取失败:', error)
    }
    return null
  }

  /**
   * 保存到本地存储
   */
  saveToLocalStorage(type, template) {
    try {
      let cacheData = wx.getStorageSync(this.LOCAL_STORAGE_KEY) || {}
      
      cacheData[type] = {
        ...template,
        source: 'local',
        cachedAt: Date.now(),
        cacheVersion: 2
      }
      
      wx.setStorageSync(this.LOCAL_STORAGE_KEY, cacheData)
      wx.setStorageSync(this.VERSION_KEY, Date.now())
      
    } catch (error) {
      console.warn('本地存储写入失败:', error)
    }
  }

  /**
   * 检查本地模板是否有效
   */
  isLocalTemplateValid(template) {
    if (!template || !template.content) return false
    
    // 检查缓存时间（本地缓存保持更长时间）
    const cacheAge = Date.now() - (template.cachedAt || 0)
    const maxAge = 24 * 60 * 60 * 1000 // 24小时
    
    return cacheAge < maxAge
  }

  /**
   * 获取硬编码模板（兜底策略）
   */
  getHardcodedTemplate(type) {
    const FALLBACK_TEMPLATES = {
      'formal': {
        id: 'formal_fallback',
        name: '正式评语模板（离线版）',
        type: 'formal',
        content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份全面、客观、个性化、充满关怀的学期综合评语。

学生姓名：
<student_name>
{studentName}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{performanceMaterial}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"总体评价 + 优点详述 + 待改进点与期望 + 结尾祝福"的结构。
2. **内容要求**：
   - **优点详述部分**：必须从素材中提炼2-3个最突出的优点，并引用具体事例来支撑，让表扬不空洞。
   - **待改进点部分**：如果素材中有相关记录，请用委婉、鼓励的语气指出，并提出具体、可行的建议。如果没有，则可以写一些普遍性的鼓励和期望。
3. **语气风格**：语言要正式、客观、专业，体现中职老师的权威性和专业性，称呼以学生名字（不带姓，如小华同学）开头。
4. **个性化**：评语必须尊重、保留且紧密结合提供的素材，体现出每个学生的独特性，严禁使用千篇一律的套话，严禁捏造素材中不存在的内容。
5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"无行为依据，无法生评语！"

请直接生成完整的评语内容，100-150字之间。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
        ],
        source: 'hardcoded',
        version: 1,
        enabled: true
      },
      'warm': {
        id: 'warm_fallback',
        name: '温馨评语模板（离线版）',
        type: 'warm',
        content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份温馨亲切、充满关怀的学期综合评语。

学生姓名：
<student_name>
{studentName}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{performanceMaterial}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"亲切问候 + 优点赞扬 + 温馨建议 + 暖心祝福"的结构。
2. **内容要求**：
   - **优点赞扬部分**：必须从素材中提炼2-3个最突出的优点，用温暖的语言表达赞扬，让学生感受到被认可。
   - **温馨建议部分**：如果素材中有需要改进的地方，请用关爱、鼓励的语气提出建议。如果没有，则给予温暖的期望和鼓励。
3. **语气风格**：语言要温馨、亲切、充满爱意，像慈母般的关怀，让学生感受到老师的温暖和关爱，称呼以学生名字（不带姓，如小华同学）开头。
4. **个性化**：评语必须尊重、保留且紧密结合提供的素材，体现出每个学生的独特性，严禁使用千篇一律的套话，严禁捏造素材中不存在的内容。
5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"无行为依据，无法生评语！"

请直接生成完整的评语内容，100-150字之间。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
        ],
        source: 'hardcoded',
        version: 1,
        enabled: true
      },
      'encouraging': {
        id: 'encouraging_fallback',
        name: '鼓励评语模板（离线版）',
        type: 'encouraging',
        content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份充满鼓励、激发潜能的学期综合评语。

学生姓名：
<student_name>
{studentName}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{performanceMaterial}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"积极开场 + 闪光点发现 + 潜能激发 + 信心鼓舞"的结构。
2. **内容要求**：
   - **闪光点发现部分**：必须从素材中挖掘2-3个最突出的优点和进步，用激励的语言放大学生的闪光点。
   - **潜能激发部分**：基于素材中的表现，鼓励学生发挥更大潜能，提出积极的期望和目标。
3. **语气风格**：语言要充满正能量、激励人心，像教练般的鼓舞，让学生充满自信和动力，称呼以学生名字（不带姓，如小华同学）开头。
4. **个性化**：评语必须尊重、保留且紧密结合提供的素材，体现出每个学生的独特性，严禁使用千篇一律的套话，严禁捏造素材中不存在的内容。
5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"无行为依据，无法生评语！"

请直接生成完整的评语内容，100-150字之间。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
        ],
        source: 'hardcoded',
        version: 1,
        enabled: true
      },
      'detailed': {
        id: 'detailed_fallback',
        name: '详细评语模板（离线版）',
        type: 'detailed',
        content: `你是一位拥有15年教龄、经验丰富、充满爱心且善于观察的中职班主任。你的任务是根据提供的学生日常表现素材，为学生撰写一份详细全面、深入分析的学期综合评语。

学生姓名：
<student_name>
{studentName}
</student_name>

学生本学期的日常表现素材如下：
<performance_material>
{performanceMaterial}
</performance_material>

请严格遵循以下要求撰写评语：
1. **评语结构**：采用"全面概述 + 详细分析 + 深度建议 + 期望展望"的结构。
2. **内容要求**：
   - **详细分析部分**：必须从素材中全面分析学生的学习态度、行为表现、人际交往等多个维度，引用具体事例进行深入分析。
   - **深度建议部分**：基于素材分析，提出具体、可操作的改进建议和发展方向，帮助学生全面成长。
3. **语气风格**：语言要详实、深入、专业，体现班主任的全面观察和深度思考，称呼以学生名字（不带姓，如小华同学）开头。
4. **个性化**：评语必须尊重、保留且紧密结合提供的素材，体现出每个学生的独特性，严禁使用千篇一律的套话，严禁捏造素材中不存在的内容。
5. **日常行为记录缺失处理要求**：当表现素材为空，则只输出"无行为依据，无法生评语！"

请直接生成完整的评语内容，100-150字之间。`,
        variables: [
          { name: 'studentName', type: 'string', required: true, description: '学生姓名' },
          { name: 'performanceMaterial', type: 'text', required: true, description: '学生表现材料' }
        ],
        source: 'hardcoded',
        version: 1,
        enabled: true
      }
    }

    const template = FALLBACK_TEMPLATES[type] || FALLBACK_TEMPLATES['warm']
    return {
      ...template,
      cachedAt: Date.now(),
      cacheVersion: 2
    }
  }

  /**
   * 检查缓存是否过期
   */
  isExpired() {
    return Date.now() - this.lastUpdate > this.CACHE_DURATION
  }

  /**
   * 预加载常用模板
   */
  async preloadTemplates() {
    const commonTypes = ['warm', 'formal', 'encouraging', 'detailed']
    
    const loadPromises = commonTypes.map(async (type) => {
      try {
        await this.getTemplate(type)
        console.log(`预加载模板成功: ${type}`)
      } catch (error) {
        console.warn(`预加载模板失败: ${type}`, error)
      }
    })

    await Promise.allSettled(loadPromises)
    console.log('模板预加载完成')
  }

  /**
   * 强制刷新缓存
   */
  async refreshCache() {
    console.log('开始刷新模板缓存')
    this.clearCache()
    await this.preloadTemplates()
    console.log('模板缓存刷新完成')
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear()
    this.lastUpdate = 0
    
    try {
      wx.removeStorageSync(this.LOCAL_STORAGE_KEY)
      wx.removeStorageSync(this.VERSION_KEY)
    } catch (error) {
      console.warn('清除本地存储失败:', error)
    }
    
    console.log('模板缓存已清除')
  }

  /**
   * 确保初始化
   */
  async ensureInitialized() {
    if (!this.initialized) {
      // 检查是否需要升级本地缓存格式
      await this.upgradeLocalCache()
      this.initialized = true
    }
  }

  /**
   * 升级本地缓存格式（兼容性处理）
   */
  async upgradeLocalCache() {
    try {
      const oldCacheKey = 'template_cache'
      const oldCache = wx.getStorageSync(oldCacheKey)
      
      if (oldCache) {
        console.log('发现旧版本缓存，正在升级...')
        // 清除旧版本缓存
        wx.removeStorageSync(oldCacheKey)
        console.log('旧版本缓存已清除')
      }
    } catch (error) {
      console.warn('缓存升级失败:', error)
    }
  }

  /**
   * 获取缓存状态信息
   */
  getCacheStatus() {
    const memoryCount = this.cache.size
    let localCount = 0
    
    try {
      const localCache = wx.getStorageSync(this.LOCAL_STORAGE_KEY)
      localCount = localCache ? Object.keys(localCache).length : 0
    } catch (error) {
      console.warn('获取本地缓存状态失败:', error)
    }

    return {
      memoryCache: {
        count: memoryCount,
        lastUpdate: this.lastUpdate,
        isExpired: this.isExpired()
      },
      localStorage: {
        count: localCount
      },
      cacheVersion: 2,
      initialized: this.initialized
    }
  }

  /**
   * 检查模板更新
   */
  async checkForUpdates() {
    try {
      const localVersion = wx.getStorageSync(this.VERSION_KEY) || 0
      
      // 这里可以实现更复杂的版本检查逻辑
      // 例如从服务器获取最新的模板版本信息
      console.log('本地模板版本:', localVersion)
      
      return {
        hasUpdates: false,
        localVersion,
        needsRefresh: this.isExpired()
      }
    } catch (error) {
      console.warn('检查更新失败:', error)
      return {
        hasUpdates: false,
        error: error.message
      }
    }
  }
}

// 创建全局实例
const templateCache = new TemplateCache()

// 导出实例
module.exports = templateCache

// 也导出类供测试使用
module.exports.TemplateCache = TemplateCache